import { useEffect, useState } from 'react'

export const useCurrentUserName = () => {
  const [name, setName] = useState<string | null>(null)

  useEffect(() => {
    const fetchProfileName = async () => {
      // TODO: Implement get user name with Convex instead of Supabase
      // const { data, error } = await createClient().auth.getUser()
      // if (error) {
      //   console.error(error)
      // }

      // setName(data.user?.user_metadata.full_name ?? '?')
    }

    fetchProfileName()
  }, [])

  return name || "?";
};
