import * as React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@curatd/ui/components/dialog";
import { Button } from "@curatd/ui/components/button";
import { Checkbox } from "@curatd/ui/components/checkbox";
import { api } from "@curatd/backend/api";
import { Doc } from "@curatd/backend/schema";
import { Enums } from "@curatd/backend";
import { useMutation } from "convex/react";
import { toast } from "sonner";
import { useI18n } from "@curatd/shared/locales/client";

const USER_ROLES = Object.values(Enums.Roles);

interface BulkManageRolesDialogProps {
  userIds: Doc<"users">["_id"][];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function BulkManageRolesDialog({
  userIds,
  open,
  onOpenChange,
  onSuccess,
}: BulkManageRolesDialogProps) {
  const t = useI18n();
  const bulkAssignRoles = useMutation(
    api.functions.useCases.admin.userManagement.users.bulkAssignRoles
  );

  const [selectedRoles, setSelectedRoles] = React.useState<string[]>([]);
  const [isSaving, setIsSaving] = React.useState(false);

  React.useEffect(() => {
    // Reset selection when dialog is reopened
    if (open) {
      setSelectedRoles([]);
    }
  }, [open]);

  const handleSave = async () => {
    if (selectedRoles.length === 0) {
      toast.error(
        t("admin.userManagement.bulkManageRolesDialog.noRoleSelectedError")
      );
      return;
    }

    setIsSaving(true);
    try {
      const rolesToAssign = [];

      for (const role of selectedRoles) {
        const foundRole = USER_ROLES.find((r) => r === role);
        if (foundRole) {
          rolesToAssign.push(foundRole);
        }
      }

      if (rolesToAssign.length > 0) {
        const { errors, success } = await bulkAssignRoles({
          roles: rolesToAssign,
          user_ids: userIds,
        });

        if (success) {
          toast.success(
            userIds.length > 1
              ? t(
                  "admin.userManagement.bulkManageRolesDialog.successMessage_plural",
                  {
                    count: userIds.length,
                  }
                )
              : t("admin.userManagement.bulkManageRolesDialog.successMessage", {
                  count: userIds.length,
                })
          );
          onSuccess();
          onOpenChange(false);
        } else {
          console.error("Failed to bulk update roles:", JSON.stringify(errors));
          toast.error(
            t("admin.userManagement.bulkManageRolesDialog.errorMessage")
          );
        }
      }
    } catch (error) {
      console.error("Failed to bulk update roles:", error);
      toast.error(t("admin.userManagement.bulkManageRolesDialog.errorMessage"));
    } finally {
      setIsSaving(false);
    }
  };

  const onRoleToggle = (checked: boolean, role: string) => {
    setSelectedRoles((prev) =>
      checked ? [...prev, role] : prev.filter((code) => code !== role)
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {userIds.length > 1
              ? t("admin.userManagement.bulkManageRolesDialog.title_plural", {
                  count: userIds.length,
                })
              : t("admin.userManagement.bulkManageRolesDialog.title", {
                  count: userIds.length,
                })}
          </DialogTitle>
          <DialogDescription>
            {t("admin.userManagement.bulkManageRolesDialog.description")}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {USER_ROLES?.map((role) => (
            <div key={role} className="flex items-center space-x-3">
              <Checkbox
                id={`role-bulk-${role}`}
                checked={selectedRoles.includes(role)}
                onCheckedChange={(checked) => onRoleToggle(!!checked, role)}
              />
              <label
                htmlFor={`role-bulk-${role}`}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {role}
              </label>
            </div>
          ))}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("admin.userManagement.manageRolesDialog.cancel")}
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving
              ? t("admin.userManagement.manageRolesDialog.saving")
              : t("admin.userManagement.bulkManageRolesDialog.assignButton")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
