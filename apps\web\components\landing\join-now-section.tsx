"use client";

import { useI18n } from "@curatd/shared/locales/client";
import { motion } from "motion/react";
import { Title } from "./title";
import { PreRegistrationForm } from "./pre-registration-form";

export function JoinNowSection() {
    const t = useI18n();

    const benefits = [
        {
            icon: (
                <svg className="w-6 h-6 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
            ),
            title: t("landing.joinNow.benefits.earlyAccess.title"),
            description: t("landing.joinNow.benefits.earlyAccess.description"),
        },
        {
            icon: (
                <svg className="w-6 h-6 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
            ),
            title: t("landing.joinNow.benefits.specialOffer.title"),
            description: t("landing.joinNow.benefits.specialOffer.description"),
        },
        {
            icon: (
                <svg className="w-6 h-6 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 110-15c2.1 0 4 .9 5.3 2.3L17 7h-2" />
                </svg>
            ),
            title: t("landing.joinNow.benefits.updates.title"),
            description: t("landing.joinNow.benefits.updates.description"),
        },
    ];

    return (
        <section id="join-now" className="py-8 sm:py-12 md:py-16 lg:py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <div className="max-w-4xl mx-auto text-center">
                    <Title
                        title={t("landing.joinNow.title")}
                        subtitle={t("landing.joinNow.subtitle")}
                        showSeparator={true}
                        animated={true}
                    />

                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.6, delay: 0.2 }}
                        className="pt-10"
                    >
                        <PreRegistrationForm
                            type="user"
                            formTitle={t("landing.joinNow.formTitle")}
                        />
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.6, delay: 0.4 }}
                        className="mt-12 sm:mt-16"
                    >
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-3xl mx-auto">
                            {benefits.map((benefit, idx) => (
                                <div className="text-center" key={idx}>
                                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                        {benefit.icon}
                                    </div>
                                    <h4 className="font-semibold text-foreground mb-2">
                                        {benefit.title}
                                    </h4>
                                    <p className="text-sm text-muted-foreground">
                                        {benefit.description}
                                    </p>
                                </div>
                            ))}
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    );
} 