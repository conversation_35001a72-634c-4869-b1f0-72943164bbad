import { NextFetchEvent, NextRequest, NextResponse } from "next/server"
import { i18n, adminAuth } from "@curatd/shared/middleware"
import { middlewareHandler } from "@curatd/shared/middleware"

const middlewares = [
    adminAuth,
    i18n,
]

export function middleware(request: NextRequest, event: NextFetchEvent, response: NextResponse) {
    return middlewareHandler(middlewares)(request, event, response)
}

export const config = {
    matcher: ['/((?!static|.*\\..*|_next|favicon.ico|robots.txt).*)']
}