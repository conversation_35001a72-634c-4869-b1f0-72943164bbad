{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"esModuleInterop": true, "isolatedModules": true, "noImplicitAny": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "lib": ["es2022", "DOM", "DOM.Iterable"], "module": "NodeNext", "moduleDetection": "force", "moduleResolution": "NodeNext", "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ES2022"}}