import { createI18nMiddleware } from 'next-international/middleware'
import type { MiddlewareWrapperType } from './types';
import { NextFetchEvent, NextRequest, NextResponse } from 'next/server';

// Create the i18n middleware once
const I18nMiddleware = createI18nMiddleware({
    locales: ['en', 'fr', 'pt'],
    defaultLocale: 'pt',
})

export const i18n: MiddlewareWrapperType = (next) => {
    return (request: NextRequest, event: NextFetchEvent, response: NextResponse) => {
        // Skip i18n for API routes
        if (request.nextUrl.pathname.startsWith('/api/')) {
            return next(request, event, response)
        }

        // Let next-international handle the locale routing
        // This will automatically redirect to the default locale if no locale is specified
        const i18nResult = I18nMiddleware(request)

        // If i18n middleware returns a response (redirect or rewrite), return it
        if (i18nResult) {
            return i18nResult
        }

        // If no i18n handling was needed, continue with the chain
        return next(request, event, response)
    }
}

