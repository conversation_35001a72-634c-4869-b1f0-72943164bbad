import { redirect } from "next/navigation";
import { getCurrentUser } from "@curatd/shared/backend/use-cases/user-management/server";
import { BusinessSidebar } from "@/components/business/business-sidebar";
import { BusinessSiteHeader } from "@/components/business/business-site-header";
import { SidebarInset, SidebarProvider } from "@curatd/ui/components/sidebar";

export default async function BusinessLayout({
  children,
  searchParams,
}: {
  children: React.ReactNode;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // This layout protects all business-related pages.
  const user = await getCurrentUser();
  const params = await searchParams;

  const roles = user.user_roles.map((ur) => ur.roles.code);

  const isBusinessUser =
    roles.includes("coach") || roles.includes("facility_manager");

  // If the user is not a coach or facility manager, they cannot access the business section.
  if (!isBusinessUser) {
    // Redirect them back to the main customer application.
    return redirect("/app");
  }

  // Handle the redirect from Stripe Connect onboarding
  if (params?.stripe_return === "true") {
    console.log("stripe_return");
    const destination = roles.includes("coach")
      ? "/app/business/coach"
      : "/app/business/facility-manager";
    return redirect(`${destination}?onboarded=true`);
  }

  // For refresh URLs, redirect to the dashboard with an error
  if (params?.stripe_refresh === "true") {
    const destination = roles.includes("coach")
      ? "/app/business/coach"
      : "/app/business/facility-manager";
    return redirect(`${destination}?onboarded=false&error=refresh`);
  }

  // User is a business user, render the business-specific layout with sidebar navigation
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <BusinessSidebar variant="inset" />
      <SidebarInset>
        <BusinessSiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {children}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
