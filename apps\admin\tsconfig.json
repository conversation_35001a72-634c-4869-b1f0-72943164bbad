{"extends": "@curatd/typescript-config/nextjs.json", "compilerOptions": {"paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@curatd/backend/api": ["../../packages/backend/convex/_generated/api"], "@curatd/backend/schema": ["../../packages/backend/convex/_generated/dataModel"], "@curatd/backend/server": ["../../packages/backend/convex/_generated/server"]}, "incremental": true}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", "next.config.js", ".next/types/**/*.ts"], "exclude": ["node_modules"]}