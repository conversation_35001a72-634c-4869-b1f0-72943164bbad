"use client";

import { useState, useCallback } from "react";
import { motion, AnimatePresence } from "motion/react";
import { ArrowLeft } from "lucide-react";
import { useI18n } from "@curatd/shared/locales/client";
import { Button } from "@curatd/ui/components/button";
import { OnboardingProgress } from "./onboarding-progress";
import { RoleSelectionStep } from "./role-selection-step";
import { OnboardingCompletion } from "./onboarding-completion";
import type { UserRole } from "./role-selection-card";

interface OnboardingFlowProps {
  onComplete: (selectedRole?: UserRole) => void;
  className?: string;
  isLoading?: boolean;
}

type OnboardingStep = "role-selection" | "completion";

export function OnboardingFlow({
  onComplete,
  className,
  isLoading = false,
}: OnboardingFlowProps) {
  const t = useI18n();
  const [currentStep, setCurrentStep] =
    useState<OnboardingStep>("role-selection");
  const [selectedRole, setSelectedRole] = useState<UserRole | undefined>();

  const steps = [
    t("onboarding.roleSelection.steps.roleSelection"),
    t("onboarding.roleSelection.steps.complete"),
  ];

  const stepIndex = currentStep === "role-selection" ? 0 : 1;

  const handleRoleSelect = useCallback((role: UserRole) => {
    setSelectedRole(role);
  }, []);

  const handleContinueFromRoleSelection = useCallback(() => {
    setCurrentStep("completion");
  }, []);

  const handleComplete = useCallback(() => {
    onComplete(selectedRole);
  }, [onComplete, selectedRole]);

  const handleBack = useCallback(() => {
    if (currentStep === "completion") {
      setCurrentStep("role-selection");
    }
  }, [currentStep]);

  return (
    <div className={className}>
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
        <div className="container mx-auto px-4 py-8">
          {/* Header with back button and progress */}
          <div className="mb-12 space-y-8">
            {/* Back button */}
            {currentStep !== "role-selection" && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex justify-start"
              >
                <Button
                  variant="ghost"
                  onClick={handleBack}
                  className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
                >
                  <ArrowLeft className="h-4 w-4" />
                  {t("onboarding.roleSelection.buttons.back")}
                </Button>
              </motion.div>
            )}

            {/* Progress indicator */}
            <OnboardingProgress steps={steps} currentStep={stepIndex} />

            {/* Main title (only on first step) */}
            {currentStep === "role-selection" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center space-y-4 pt-8"
              >
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                  {t("onboarding.title")}
                </h1>
                <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto">
                  {t("onboarding.subtitle")}
                </p>
              </motion.div>
            )}
          </div>

          {/* Step content */}
          <div className="flex-1 flex items-center justify-center">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="w-full"
              >
                {currentStep === "role-selection" && (
                  <RoleSelectionStep
                    onRoleSelect={handleRoleSelect}
                    onContinue={handleContinueFromRoleSelection}
                    selectedRole={selectedRole}
                  />
                )}

                {currentStep === "completion" && (
                  <OnboardingCompletion
                    onComplete={!isLoading ? handleComplete : () => {}}
                  />
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}
