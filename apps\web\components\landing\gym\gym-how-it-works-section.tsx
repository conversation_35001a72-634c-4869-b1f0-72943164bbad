"use client";

import React from "react";
import { useI18n } from "@curatd/shared/locales/client";
import { Title } from "../title";
import { Hotel, List, Users } from "lucide-react";
import { HowItWorksSectionGeneric } from "../how-it-works-section-generic";

export function GymHowItWorksSection() {
    const t = useI18n();

    const steps = [
        {
            number: "01",
            icon: <Hotel className="w-8 h-8 text-foreground" />,
            title: t("landing.gym.howItWorks.steps.registerGym.title"),
            description: t("landing.gym.howItWorks.steps.registerGym.description"),
        },
        {
            number: "02",
            icon: <List className="w-8 h-8 text-foreground" />,
            title: t("landing.gym.howItWorks.steps.review.title"),
            description: t("landing.gym.howItWorks.steps.review.description"),
        },
        {
            number: "03",
            icon: <Users className="w-8 h-8 text-foreground" />,
            title: t("landing.gym.howItWorks.steps.welcomeUsers.title"),
            description: t("landing.gym.howItWorks.steps.welcomeUsers.description"),
        }
    ];

    return (
        <HowItWorksSectionGeneric
            title={t("landing.gym.howItWorks.title")}
            steps={steps}
            columns={3}
        />
    );
} 