import type { Metada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import { ThemeProvider } from "@curatd/ui/components/theme-provider";
import { Toaster } from "@curatd/ui/components/sonner";
import "@curatd/ui/globals.css";
import AuthServerProvider from "@curatd/ui/components/auth-server-provider";
import ConvexClientProvider from "@curatd/ui/components/convex-client-provider";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: "600",
});

export const metadata: Metadata = {
  title: "Curatd Admin",
  description: "Curatd Admin",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <AuthServerProvider>
      <html lang="en" suppressHydrationWarning>
        <body
          className={`${poppins.variable} antialiased bg-background text-foreground`}
        >
          <ThemeProvider>
            <ConvexClientProvider>{children}</ConvexClientProvider>
            <Toaster />
          </ThemeProvider>
        </body>
      </html>
    </AuthServerProvider>
  );
}
