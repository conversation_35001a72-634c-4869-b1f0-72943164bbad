"use client";

import { motion } from "motion/react";
import { cn } from "@curatd/ui/lib/utils";

interface TitleProps {
    title: string;
    titleHighlight?: string;
    subtitle?: string;
    showSeparator?: boolean;
    centered?: boolean;
    animated?: boolean;
    className?: string;
    titleClassName?: string;
    subtitleClassName?: string;
    separatorClassName?: string;
    size?: "sm" | "md" | "lg" | "xl";
    as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
}

export function Title({
    title,
    titleHighlight,
    subtitle,
    showSeparator = false,
    centered = true,
    animated = true,
    className,
    titleClassName,
    subtitleClassName,
    separatorClassName,
    size = "md",
    as = "h2"
}: TitleProps) {
    const sizeClasses = {
        sm: "text-2xl sm:text-3xl md:text-4xl lg:text-5xl",
        md: "text-3xl sm:text-4xl md:text-5xl lg:text-6xl",
        lg: "text-3xl sm:text-4xl md:text-5xl lg:text-6xl",
        xl: "text-4xl sm:text-5xl md:text-6xl lg:text-7xl"
    };

    const TitleContent = () => {
        const HeadingTag = as;

        return (
            <div className={cn(
                "space-y-4 sm:space-y-6 md:space-y-8",
                centered && "text-center",
                className
            )}>
                <HeadingTag className={cn(
                    sizeClasses[size],
                    "font-bold mb-6 sm:mb-8 text-foreground tracking-wide",
                    titleClassName
                )}>
                    {title}{titleHighlight && <span> {titleHighlight}</span>}
                </HeadingTag>

                {showSeparator && (
                    <div className={cn(
                        "w-16 sm:w-20 md:w-24 h-px bg-border mb-6 sm:mb-8",
                        centered && "mx-auto",
                        separatorClassName
                    )} />
                )}

                {subtitle && (
                    <p className={cn(
                        "text-lg sm:text-xl md:text-2xl text-muted-foreground max-w-3xl",
                        centered && "mx-auto",
                        subtitleClassName
                    )}>
                        {subtitle}
                    </p>
                )}
            </div>
        );
    };

    if (animated) {
        return (
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="mb-16"
            >
                <TitleContent />
            </motion.div>
        );
    }

    return (
        <div className="mb-16">
            <TitleContent />
        </div>
    );
} 