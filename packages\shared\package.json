{"name": "@curatd/shared", "version": "0.1.0", "exports": {"./config": "./src/config.ts", "./env": "./src/env.ts", "./middleware": "./src/middleware/index.ts", "./locales/client": "./src/locales/client.ts", "./locales/server": "./src/locales/server.ts", "./locales": "./src/locales/available-locales.ts", "./resend": "./src/resend.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "dependencies": {"@auth/core": "^0.40.0", "@convex-dev/auth": "^0.0.87", "@convex-dev/eslint-plugin": "0.0.1-alpha.4", "@react-native-async-storage/async-storage": "^2.2.0", "@simplewebauthn/server": "^13.1.2", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "convex": "^1.25.4", "convex-helpers": "^0.1.99", "next": "^15.3.4", "next-international": "^1.3.1", "oslo": "^1.2.1", "react": "^19.1.0", "resend": "^4.6.0", "stripe": "^17.7.0", "zod": "^3.25.67"}, "devDependencies": {"@curatd/typescript-config": "workspace:*", "typescript": "5.8.2"}, "peerDependencies": {"react": "^19.1.0"}}