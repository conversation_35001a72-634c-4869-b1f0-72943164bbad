import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { useI18n } from "@curatd/shared/locales/client";
import { createInviteDialogErrorHandler } from "../../lib/error-handler";

import { Button } from "@curatd/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@curatd/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@curatd/ui/components/form";
import { Input } from "@curatd/ui/components/input";
import { Checkbox } from "@curatd/ui/components/checkbox";
import { Badge } from "@curatd/ui/components/badge";
import { Enums } from "@curatd/backend";

const USER_ROLES = Object.values(Enums.Roles);

type InviteUserForm = {
  email: string;
  displayName?: string;
  roles: Enums.Roles[];
};

interface InviteUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function InviteUserDialog({
  open,
  onOpenChange,
  onSuccess,
}: InviteUserDialogProps) {
  const t = useI18n();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const inviteUserSchemaWithI18n = z.object({
    email: z
      .string()
      .email(t("admin.userManagement.inviteDialog.pleaseEnterValidEmail")),
    displayName: z.string().optional(),
    roleCodes: z
      .array(z.string())
      .min(
        1,
        t("admin.userManagement.inviteDialog.pleaseSelectAtLeastOneRole")
      ),
  });

  const form = useForm<InviteUserForm>({
    resolver: zodResolver(inviteUserSchemaWithI18n),
    defaultValues: {
      email: "",
      displayName: "",
      roles: [],
    },
  });

  const selectedRoles = form.watch("roles");

  // Create error handler instance
  const errorHandler = React.useMemo(
    () => createInviteDialogErrorHandler({ t }),
    [t]
  );

  const handleSubmit = async (data: InviteUserForm) => {
    setIsSubmitting(true);
    try {
      const inviteData: InviteUserInput = {
        email: data.email,
        displayName: data.displayName || undefined,
        roles: data.roles,
      };

      const response = await fetch("/api/users/invite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(inviteData),
      });

      // Use error handler to process the response
      await errorHandler.handleAPIResponse(response);

      toast.success(t("admin.userManagement.inviteDialog.invitationSent"));
      form.reset();
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      // Error from handleAPIResponse already contains the correct translated message
      const errorMessage =
        error instanceof Error
          ? error.message
          : t("admin.userManagement.inviteDialog.errors.genericError");

      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleRole = (role: Enums.Roles) => {
    const currentRoles = form.getValues("roles");
    const newRoles = currentRoles.includes(role)
      ? currentRoles.filter((r) => r !== role)
      : [...currentRoles, role];

    form.setValue("roles", newRoles);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {t("admin.userManagement.inviteDialog.title")}
          </DialogTitle>
          <DialogDescription>
            {t("admin.userManagement.inviteDialog.description")}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <div className="space-y-4">
              {/* Email */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("admin.userManagement.inviteDialog.emailLabel")} *
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t(
                          "admin.userManagement.inviteDialog.emailPlaceholder"
                        )}
                        type="email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Display Name */}
              <FormField
                control={form.control}
                name="displayName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("admin.userManagement.inviteDialog.displayNameLabel")}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t(
                          "admin.userManagement.inviteDialog.displayNamePlaceholder"
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Roles */}
              <FormField
                control={form.control}
                name="roles"
                render={() => (
                  <FormItem>
                    <FormLabel>
                      {t("admin.userManagement.inviteDialog.rolesLabel")} *
                    </FormLabel>
                    <div className="space-y-3">
                      <div className="grid grid-cols-1 gap-3">
                        {USER_ROLES.map((role) => (
                          <div
                            key={role}
                            className="flex items-center space-x-3 rounded-lg border p-3"
                          >
                            <Checkbox
                              checked={selectedRoles.includes(role)}
                              onCheckedChange={() => toggleRole(role)}
                            />
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <Badge variant="outline" className="text-xs">
                                  {role}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Selected Roles Summary */}
                      {selectedRoles.length > 0 && (
                        <div className="mt-3">
                          <p className="text-sm font-medium text-muted-foreground mb-2">
                            {t(
                              "admin.userManagement.inviteDialog.selectedRoles"
                            )}
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {selectedRoles.map((role) => (
                              <Badge
                                key={role}
                                variant="secondary"
                                className="text-xs"
                              >
                                {role}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                {t("admin.userManagement.inviteDialog.cancel")}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? t("admin.userManagement.inviteDialog.sending")
                  : t("admin.userManagement.inviteDialog.sendInvitation")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
