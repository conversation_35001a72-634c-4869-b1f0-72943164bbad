import { QueryCtx, MutationCtx } from "@/convex/_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Doc } from "@/convex/_generated/dataModel";

export async function userHasRole(ctx: QueryCtx | MutationCtx, role: Doc<"users">["roles"][number]) {
    const userId = await getAuthUserId(ctx);
    if (userId === null) {
        return false;
    }
    const user = await ctx.db.get(userId);
    if (!user) {
        return false;
    }
    return user.roles.includes(role);
}
