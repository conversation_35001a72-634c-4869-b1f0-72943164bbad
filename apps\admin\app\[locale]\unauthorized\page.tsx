import Link from "next/link";
import { <PERSON><PERSON> } from "@curatd/ui/components/button";
import { getI18n } from "@curatd/shared/locales/server";
import { IconShieldX, IconHome } from "@tabler/icons-react";
import { getAppUrlsConfig } from "@curatd/shared/config";
import { LogoutButton } from "@curatd/ui/components/logout-button";

export default async function UnauthorizedPage() {
  const t = await getI18n();

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <IconShieldX className="mx-auto h-24 w-24 text-destructive" />
          <h2 className="mt-6 text-3xl font-bold tracking-tight">
            {t("admin.unauthorized.title")}
          </h2>
          <p className="mt-2 text-sm text-muted-foreground">
            {t("admin.unauthorized.description")}
          </p>
        </div>

        <div className="space-y-4">
          <div className="rounded-lg border border-border bg-card p-4">
            <h3 className="font-medium text-sm mb-2">
              {t("admin.unauthorized.needAccess")}
            </h3>
            <p className="text-xs text-muted-foreground">
              {t("admin.unauthorized.contactAdmin")}
            </p>
          </div>

          <div className="flex flex-col gap-2 w-full">
            <Link
              href={`${getAppUrlsConfig().NEXT_PUBLIC_WEB_APP_URL || ""}/app`}
              className="block"
            >
              <Button className="w-full" variant="outline">
                <IconHome className="mr-2 h-4 w-4" />
                {t("admin.unauthorized.returnToMain")}
              </Button>
            </Link>
            <LogoutButton />
          </div>
        </div>
      </div>
    </div>
  );
}
