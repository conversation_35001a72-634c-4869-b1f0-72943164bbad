import { CarouselSection } from "@/components/landing/carousel-section";
import { Footer } from "@/components/landing/footer";
import { HeroSection } from "@/components/landing/hero-section";
import { HowItWorksSection } from "@/components/landing/how-it-works-section";
import { JoinNowSection } from "@/components/landing/join-now-section";
import { Navbar } from "@/components/landing/navbar";
import { PlaceSection } from "@/components/landing/place-section";
import { QNASection } from "@/components/landing/qna-section";

export default async function LandingPage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Curatd",
    "url": "https://curatd.com",
    "description": "Private wellness platform connecting coaches, gyms, and customers for personal fitness sessions",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://curatd.com/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "@id": "https://curatd.com/#organization"
    }
  };

  const serviceData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Personal Fitness Training",
    "description": "Book personal fitness sessions at home, outdoors, or in select partner gyms",
    "provider": {
      "@type": "Organization",
      "name": "Curatd",
      "@id": "https://curatd.com/#organization"
    },
    "areaServed": {
      "@type": "Place",
      "name": "Global"
    },
    "serviceType": "Fitness Training",
    "availableChannel": {
      "@type": "ServiceChannel",
      "serviceUrl": "https://curatd.com",
      "serviceSmsNumber": "+1-800-CURATD",
      "serviceLocation": {
        "@type": "Place",
        "name": "Client Location"
      }
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceData),
        }}
      />
      <div className="min-h-screen bg-background text-foreground">
        <Navbar isHome={true} />
        <div className="flex flex-col gap-6 md:gap-0 mx-auto">
          <HeroSection />
          <HowItWorksSection />
          <CarouselSection />
          <PlaceSection />
          <JoinNowSection />
          <QNASection />
        </div>
        <Footer />
        <div className="h-[var(--navbar-height)] md:hidden" />
      </div>
    </>
  );
}

export const metadata = {
  title: "Curatd – Connect with Coaches, Gyms, and More",
  description: "Join Curatd to discover top coaches, gyms, and exclusive offers. Early access, special deals, and the latest updates await!",
  alternates: {
    canonical: "https://curatd.com",
  },
  openGraph: {
    title: "Curatd – Connect with Coaches, Gyms, and More",
    description: "Join Curatd to discover top coaches, gyms, and exclusive offers. Early access, special deals, and the latest updates await!",
    type: "website",
    url: "https://curatd.com",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Curatd - Private wellness platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Curatd – Connect with Coaches, Gyms, and More",
    description: "Join Curatd to discover top coaches, gyms, and exclusive offers. Early access, special deals, and the latest updates await!",
    images: ["/og-image.jpg"],
  },
};
