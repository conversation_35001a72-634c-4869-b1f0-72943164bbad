import { QueryCtx, MutationCtx } from "../../convex/_generated/server";
import { userHasRole } from "../../helpers/auth/userHasRole";
import { getCurrentUser } from "../../helpers/auth/getCurrentUser";
import { Roles } from "../../types/enums";

export function withRole<T extends QueryCtx | MutationCtx, R>(role: Roles, func: (ctx: T, ...args: any[]) => Promise<R>) {
    return async (ctx: T, args: any) => {
        const user = await getCurrentUser(ctx);
        if (!user) {
            throw new Error("User not found");
        }
        if (!userHasRole(ctx, role)) {
            throw new Error(`User does not have required role: ${role}`);
        }
        return func(ctx, args);
    }
}