"use client";

import { Navbar } from "@/components/landing/navbar";
import { Footer } from "@/components/landing/footer";
import { But<PERSON> } from "@curatd/ui/components/button";
import { LandingHero } from "@/components/landing/landing-hero";
import CuratdLogo from "@curatd/ui/components/curatd-logo";
import Link from "next/link";
import { Home, ArrowLeft } from "lucide-react";
import { useI18n } from "@curatd/shared/locales/client";

export default function LocaleNotFoundPage() {
    const t = useI18n();

    const structuredData = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Page Not Found",
        "description": "The page you're looking for doesn't exist.",
        "mainEntity": {
            "@type": "Thing",
            "name": "404 Error",
            "description": "Page not found error"
        }
    };

    return (
        <>
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(structuredData),
                }}
            />
            <div className="min-h-screen bg-background text-foreground">
                <Navbar isHome={false} />

                <LandingHero
                    title={t("errors.404.title") || "Oops! Page Not Found"}
                    subtitle={t("errors.404.subtitle") || "The page you're looking for seems to have wandered off on its own fitness journey."}
                    backgroundVideoSrc="https://zfsumsiiysouykyx.public.blob.vercel-storage.com/hero-home_compressed.mp4"
                    logo={<CuratdLogo size="extra-large" color="white" />}
                    className="min-h-[70vh]"
                    mobileContent={
                        <div className="px-6 text-center space-y-6 pt-6">
                            <div className="text-8xl sm:text-9xl font-bold text-primary-foreground/20 mb-4">
                                404
                            </div>
                            <h1 className="text-3xl text-primary-foreground text-shadow-[0px_4px_8px_rgb(0_0_0_/_0.3)]">
                                {t("errors.404.title") || "Oops! Page Not Found"}
                            </h1>
                            <p className="text-lg font-light text-primary-foreground/90 text-shadow-[0px_2px_4px_rgb(0_0_0_/_0.3)]">
                                {t("errors.404.subtitle") || "The page you're looking for seems to have wandered off on its own fitness journey."}
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                                <Button
                                    asChild
                                    size="lg"
                                    className="bg-primary-foreground text-secondary px-8 py-4 text-base rounded-full font-normal hover:bg-foreground/80"
                                >
                                    <Link href="/">
                                        <Home className="w-5 h-5 mr-2" />
                                        {t("errors.404.goHome") || "Go Home"}
                                    </Link>
                                </Button>
                                <Button
                                    asChild
                                    size="lg"
                                    className="bg-primary-foreground text-primary hover:bg-primary hover:text-primary-foreground px-8 py-4 text-base rounded-full font-normal"
                                >
                                    <Link href="javascript:history.back()">
                                        <ArrowLeft className="w-5 h-5 mr-2" />
                                        {t("errors.404.goBack") || "Go Back"}
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    }
                    desktopContent={
                        <div className="max-w-7xl flex items-center justify-center px-10">
                            <div className="text-center space-y-8">
                                <div className="text-9xl lg:text-[12rem] font-bold text-primary-foreground/20 mb-8">
                                    404
                                </div>
                                <h1 className="text-4xl lg:text-6xl font-bold text-primary-foreground text-shadow-[0px_8px_8px_rgb(0_0_0_/_0.15)]">
                                    {t("errors.404.title") || "Oops! Page Not Found"}
                                </h1>
                                <p className="text-primary-foreground text-xl lg:text-2xl text-shadow-[0px_8px_8px_rgb(0_0_0_/_0.15)] max-w-3xl mx-auto">
                                    {t("errors.404.description") || "The page you're looking for seems to have wandered off on its own fitness journey. Let's get you back on track!"}
                                </p>
                                <div className="flex flex-col sm:flex-row gap-6 justify-center pt-10">
                                    <Button
                                        asChild
                                        size="lg"
                                        className="bg-primary-foreground text-secondary px-12 py-6 text-xl rounded-full font-semibold hover:bg-foreground/80"
                                    >
                                        <Link href="/">
                                            <Home className="w-6 h-6 mr-3" />
                                            {t("errors.404.goHome") || "Go Home"}
                                        </Link>
                                    </Button>
                                    <Button
                                        asChild
                                        size="lg"
                                        variant="outline"
                                        className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-secondary px-12 py-6 text-xl rounded-full font-semibold"
                                    >
                                        <Link href="javascript:history.back()">
                                            <ArrowLeft className="w-6 h-6 mr-3" />
                                            {t("errors.404.goBack") || "Go Back"}
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    }
                />

                {/* Additional helpful links section */}
                <section className="py-16 bg-muted/50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                        <h2 className="text-2xl font-bold mb-8 text-foreground">
                            {t("errors.404.lookingFor") || "Looking for something specific?"}
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="p-6 rounded-lg bg-card border border-border hover:border-primary/20 transition-all">
                                <h3 className="text-lg font-semibold mb-3">{t("nav.athlete") || "For Athletes"}</h3>
                                <p className="text-muted-foreground mb-4">
                                    {t("errors.404.athleteDesc") || "Book personal training sessions"}
                                </p>
                                <Button asChild variant="outline" className="rounded-full">
                                    <Link href="/">{t("errors.404.startTraining") || "Start Training"}</Link>
                                </Button>
                            </div>
                            <div className="p-6 rounded-lg bg-card border border-border hover:border-primary/20 transition-all">
                                <h3 className="text-lg font-semibold mb-3">{t("nav.coach") || "For Coaches"}</h3>
                                <p className="text-muted-foreground mb-4">
                                    {t("errors.404.coachDesc") || "Join and grow your business"}
                                </p>
                                <Button asChild variant="outline" className="rounded-full">
                                    <Link href="/coach">{t("errors.404.becomeCoach") || "Become a Coach"}</Link>
                                </Button>
                            </div>
                            <div className="p-6 rounded-lg bg-card border border-border hover:border-primary/20 transition-all">
                                <h3 className="text-lg font-semibold mb-3">{t("nav.gym") || "For Gyms"}</h3>
                                <p className="text-muted-foreground mb-4">
                                    {t("errors.404.gymDesc") || "Partner with us"}
                                </p>
                                <Button asChild variant="outline" className="rounded-full">
                                    <Link href="/gym">{t("errors.404.partnerNow") || "Partner Now"}</Link>
                                </Button>
                            </div>
                        </div>
                    </div>
                </section>

                <Footer />
                <div className="h-[var(--navbar-height)] md:hidden" />
            </div>
        </>
    );
} 