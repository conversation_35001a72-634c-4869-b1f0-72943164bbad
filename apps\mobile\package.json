{"name": "mobile", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@curatd/shared": "workspace:*", "@react-navigation/native": "^7.1.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "expo": "54.0.0-canary-20250613-b29d676", "expo-constants": "~17.1.7-canary-20250613-b29d676", "expo-dev-client": "5.1.9-canary-20250613-b29d676", "expo-linking": "~7.1.6-canary-20250613-b29d676", "expo-router": "5.2.0-canary-20250613-b29d676", "expo-status-bar": "2.2.4-canary-20250613-b29d676", "lucide-react-native": "^0.525.0", "nativewind": "^4.1.23", "react": "^19.1.0", "react-native": "0.80.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.27.4", "@types/react": "^19.1.8", "typescript": "~5.8.3"}, "private": true}