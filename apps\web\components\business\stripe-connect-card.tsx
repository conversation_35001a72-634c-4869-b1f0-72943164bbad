"use client";

import { toast } from "sonner";
import {
  <PERSON>,
  Card<PERSON><PERSON><PERSON>,
  CardTitle,
  CardDescription,
  CardContent,
} from "@curatd/ui/components/card";
import { Button } from "@curatd/ui/components/button";
import { Badge } from "@curatd/ui/components/badge";
import { useCreateStripeAccountLink } from "@curatd/shared/backend/use-cases/stripe-connect/client";
import { type ProfileType } from "@curatd/shared/backend/use-cases/stripe-connect/core";
import { Loader2 } from "lucide-react";

interface StripeConnectCardProps {
  profileType: ProfileType;
  stripeAccountId: string | null | undefined;
  stripeAccountStatus: string | null | undefined;
}

const getStatusVariant = (status: string | null | undefined) => {
  switch (status) {
    case "active":
      return "default";
    case "onboarding":
      return "secondary";
    case "restricted":
    case "inactive":
      return "destructive";
    default:
      return "outline";
  }
};

export function StripeConnectCard({
  profileType,
  stripeAccountId,
  stripeAccountStatus,
}: StripeConnectCardProps) {
  const { createLink, isConnecting } = useCreateStripeAccountLink();

  const handleConnect = async () => {
    const { success, data, error } = await createLink({ profileType });

    if (success && data?.url) {
      window.location.href = data.url;
    } else {
      toast.error(error || "Failed to create Stripe connection link.");
    }
  };

  const isConnected = !!stripeAccountId;
  const status = stripeAccountStatus || "not_connected";

  return (
    <Card>
      <CardHeader>
        <CardTitle>Stripe Connect</CardTitle>
        <CardDescription>
          Manage your Stripe account to receive payments for your services.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between rounded-lg border p-4">
          <div>
            <p className="text-sm font-medium">Status</p>
            <p className="text-xs text-muted-foreground">
              Your account is currently {status.replace("_", " ")}.
            </p>
          </div>
          <Badge variant={getStatusVariant(status)}>
            {status.charAt(0).toUpperCase() + status.slice(1).replace("_", " ")}
          </Badge>
        </div>
        {!isConnected ? (
          <Button
            onClick={handleConnect}
            disabled={isConnecting}
            className="w-full"
          >
            {isConnecting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Connecting...
              </>
            ) : (
              "Connect with Stripe"
            )}
          </Button>
        ) : (
          <Button variant="outline" onClick={handleConnect} className="w-full">
            Manage Stripe Account
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
