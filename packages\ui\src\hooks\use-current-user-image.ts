import { useEffect, useState } from 'react'

export const useCurrentUserImage = () => {
  const [image, setImage] = useState<string | null>(null)

  useEffect(() => {
    const fetchUserImage = async () => {
      // TODO: Implement get user image with Convex instead of Supabase
      // const { data, error } = await createClient().auth.getUser()
      // if (error) {
      //   console.error(error)
      // }

      // setImage(data.user?.user_metadata.avatar_url ?? null)
    }
    fetchUserImage()
  }, [])

  return image
}
