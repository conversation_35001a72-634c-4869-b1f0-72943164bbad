export enum Roles {
    ADMIN = "admin",
    FACILITY_MANAGER = "facility_manager",
    COACH = "coach",
    CUSTOMER = "customer"
}

export enum InvitationStatus {
    PENDING = "pending",
    ACCEPTED = "accepted",
    EXPIRED = "expired",
    CANCELLED = "cancelled"
}

export enum DocumentVerificationType {
    PASSPORT = "passport",
    ID_CARD = "id_card",
    DRIVING_LICENSE = "driving_license",
    RESIDENCE_PERMIT = "residence_permit"
}

export enum FacilityType {
    GYM = "gym",
    STADIUM = "stadium",
    COURT = "court",
    POOL = "pool",
    OTHER = "other"
}

export enum SubscriptionStatus {
    ACTIVE = "active",
    CANCELLED = "cancelled"
}

export enum ProductVariantType {
    PRIVATE = "private",
    BUDDY = "buddy",
    GROUP = "group"
}

export enum AddonsOwnerType {
    PRODUCT_VARIANT = "product_variant",
    FACILITY_ASSET = "facility_asset"
}

export enum OrderStatus {
    PENDING = "pending",
    CONFIRMED = "confirmed",
    CANCELLED = "cancelled",
    COMPLETED = "completed"
}

export enum PaymentStatus {
    UNPAID = "unpaid",
    PAID = "paid",
    FAILED = "failed"
}

export enum DiscountMethod {
    PERCENTAGE = "percentage",
    FIXED = "fixed",
    BUY_X_GET_Y = "buy_X_get_Y"
}

export enum NotificationStatus {
    QUEUED = "queued",
    SENT = "sent",
    DELIVERED = "delivered",
    READ = "read",
    FAILED = "failed"
}

export enum WaitingListIntent {
    COACH_INTEREST = "coach_interest",
    FACILITY_MANAGER_INTEREST = "facility_manager_interest",
    CUSTOMER_INTEREST = "customer_interest",
    NEWSLETTER = "newsletter",
    BETA_FEATURE = "beta_feature"
}

export enum StripeConnectStatus {
    ACTIVE = "active",
    ONBOARDING = "onboarding",
    RESTRICTED = "restricted",
    RESTRICTED_SOON = "restricted_soon",
    IN_REVIEW = "in_review",
    REJECTED = "rejected",
    INACTIVE = "inactive",
    NOT_CONNECTED = "not_connected"
}