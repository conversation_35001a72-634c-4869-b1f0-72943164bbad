import { CoachHeroSection, CoachBenefitsSection, CoachHowItWorksSection, CoachSignUpSection } from '@/components/landing/coach';
import { Navbar } from "@/components/landing/navbar";
import { Footer } from "@/components/landing/footer";

export default function CoachPage() {
    const coachStructuredData = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Curatd for Coaches",
        "description": "Join Curatd as a coach to grow your fitness business, find clients, and work flexibly",
        "url": "https://curatd.com/coach",
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://curatd.com"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "For Coaches",
                    "item": "https://curatd.com/coach"
                }
            ]
        },
        "mainEntity": {
            "@type": "JobPosting",
            "title": "Fitness Coach",
            "description": "Join a premium platform built for top coaches. Set your schedule, work flexibly, and earn more.",
            "employmentType": "CONTRACT",
            "hiringOrganization": {
                "@type": "Organization",
                "name": "Curatd",
                "@id": "https://curatd.com/#organization"
            },
            "jobLocation": {
                "@type": "Place",
                "name": "Remote/Client Location"
            },
            "workHours": "Flexible",
            "benefits": [
                "Flexible scheduling",
                "Work from anywhere",
                "Client acquisition support",
                "Marketing and booking handled"
            ]
        }
    };

    return (
        <>
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(coachStructuredData),
                }}
            />
            <main className="min-h-screen">
                <Navbar isHome={false} />
                <CoachHeroSection />
                <CoachHowItWorksSection />
                <CoachBenefitsSection />
                <CoachSignUpSection />
                <Footer />
            </main>
        </>
    );
}

export const metadata = {
    title: "Curatd for Coaches – Find Clients and Opportunities",
    description: "Curatd empowers coaches to reach new clients, manage sessions, and grow their business. Sign up for early access and special offers!",
    alternates: {
        canonical: "https://curatd.com/coach",
    },
    openGraph: {
        title: "Curatd for Coaches – Find Clients and Opportunities",
        description: "Curatd empowers coaches to reach new clients, manage sessions, and grow their business. Sign up for early access and special offers!",
        type: "website",
        url: "https://curatd.com/coach",
        images: [
            {
                url: "/og-image.jpg",
                width: 1200,
                height: 630,
                alt: "Curatd for Coaches",
            },
        ],
    },
    twitter: {
        card: "summary_large_image",
        title: "Curatd for Coaches – Find Clients and Opportunities",
        description: "Curatd empowers coaches to reach new clients, manage sessions, and grow their business. Sign up for early access and special offers!",
        images: ["/og-image.jpg"],
    },
}; 