---
title: Function calling
layout: ../../layouts/MainLayout.astro
---

# Function Calling

When one function needs to call another, follow these guidelines:

- Use `ctx.runQuery` to call a query from a query, mutation, or action.
- Use `ctx.runMutation` to call a mutation from a mutation or action.
- Use `ctx.runAction` to call an action from an action.
- All these calls require a `FunctionReference` from the generated API.

```typescript
import { mutation } from "./_generated/server";
import { api, internal } from "./_generated/api";

export const createUserWithProfile = mutation({
  args: {
    /* ... */
  },
  returns: v.id("users"),
  handler: async (ctx, args) => {
    // Create the user
    const userId = await ctx.db.insert("users", {
      name: args.name,
      email: args.email,
    });

    // Call another mutation to create profile
    await ctx.runMutation(api.profiles.create, {
      userId: userId,
      bio: args.bio,
    });

    return userId;
  },
});
```

### Important Rules

- ONL<PERSON> call an action from another action if you need to cross runtimes (e.g., from V8 to Node).
- For shared code within the same runtime, create a helper async function instead.
- Minimize calls from actions to queries and mutations to avoid race conditions.
- When using function references in the same file, add type annotations:

```typescript
export const getUser = query({
  args: { id: v.id("users") },
  returns: v.object({
    /* ... */
  }),
  handler: async (ctx, args) => {
    // ...
  },
});

export const getUsers = query({
  args: { ids: v.array(v.id("users")) },
  returns: v.array(
    v.object({
      /* ... */
    })
  ),
  handler: async (ctx, args) => {
    const results = [];
    for (const id of args.ids) {
      // Type annotation needed for circular reference
      const user: { name: string } = await ctx.runQuery(api.users.getUser, {
        id,
      });
      results.push(user);
    }
    return results;
  },
});
```
