---
title: Creating New Use Cases
layout: ../../layouts/MainLayout.astro
---

# Creating New Use Cases

This guide explains how to create new backend use cases following the established architecture and Convex best practices.

## What is a Use Case?

In our architecture, a "use case" represents a specific business operation or user interaction that requires backend functionality. Use cases are organized in a domain-driven structure, following Convex's file-based routing system.

## Use Case Structure

Use cases follow a consistent structure:

```
convex/functions/useCases/[domain]/[feature]/[operation].ts
```

For example:

- `admin/userManagement/users.ts` - User management operations for admins
- `coach/sessions/scheduling.ts` - Session scheduling operations for coaches

## Step-by-Step Guide

### 1. Define DTOs (Data Transfer Objects)

Start by defining the input and output types for your use case in the `types/dtos` directory:

```typescript
// Create a new file or add to an existing file in types/dtos/[domain]/
import { v } from "convex/values";
import { Id } from "../../convex/_generated/dataModel";

// Request DTO
export type MyFeatureRequestDTO = {
  param1: string;
  param2: number;
};

// Validator for the request DTO
export const MyFeatureRequestDTOObjectValidator = {
  param1: v.string(),
  param2: v.number(),
};

// Response DTO
export type MyFeatureResponseDTO = {
  result: boolean;
  data: string;
};

// Validator for the response DTO
export const MyFeatureResponseDTOValidator = v.object({
  result: v.boolean(),
  data: v.string(),
});
```

### 2. Create the Use Case File

Create a new file for your use case in the appropriate domain directory:

```typescript
// convex/functions/useCases/[domain]/[feature]/[operation].ts
import { query, mutation } from "../../../../../convex/_generated/server";
import { withRole } from "../../../../../wrappers/auth/withRole";
import { Roles } from "../../../../../types/enums";
import {
  MyFeatureRequestDTO,
  MyFeatureRequestDTOObjectValidator,
  MyFeatureResponseDTO,
  MyFeatureResponseDTOValidator,
} from "../../../../../types/dtos";
import { v } from "convex/values";

// Example query function - always include args, returns and handler
export const myFeature = query({
  args: MyFeatureRequestDTOObjectValidator,
  returns: MyFeatureResponseDTOValidator,
  handler: withRole(
    Roles.CUSTOMER,
    async (ctx, args: MyFeatureRequestDTO): Promise<MyFeatureResponseDTO> => {
      // Implementation...

      return {
        result: true,
        data: "Success",
      };
    }
  ),
});

// Example mutation function with proper validation
export const updateMyFeature = mutation({
  args: MyFeatureRequestDTOObjectValidator,
  returns: MyFeatureResponseDTOValidator,
  handler: withRole(
    Roles.CUSTOMER,
    async (ctx, args: MyFeatureRequestDTO): Promise<MyFeatureResponseDTO> => {
      // Validate the document exists before updating
      const document = await ctx.db.get(args.documentId);
      if (!document) {
        throw new Error("Document not found");
      }

      // Perform the update
      await ctx.db.patch(args.documentId, {
        field: args.newValue,
        updatedAt: Date.now(),
      });

      return {
        result: true,
        data: "Updated successfully",
      };
    }
  ),
});

// Example internal function (private) - not accessible from client
export const internalHelper = internalMutation({
  args: {
    id: v.id("users"),
    value: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Implementation...
    return null;
  },
});
```

### 3. Create Helper Functions (Optional)

If your use case requires complex logic, create helper functions in the appropriate directory:

```typescript
// helpers/[domain]/[feature].ts
import { QueryCtx, MutationCtx } from "../../convex/_generated/server";
import { Doc, Id } from "../../convex/_generated/dataModel";

// Helper with proper TypeScript typing
export async function myHelperFunction(
  ctx: QueryCtx | MutationCtx,
  userId: Id<"users">
): Promise<Doc<"users"> | null> {
  // Helper implementation
  return await ctx.db.get(userId);
}

// Helper with Record type using proper typing
export async function getUsernameMap(
  ctx: QueryCtx | MutationCtx,
  userIds: Id<"users">[]
): Promise<Record<Id<"users">, string>> {
  const idToUsername: Record<Id<"users">, string> = {};

  for (const userId of userIds) {
    const user = await ctx.db.get(userId);
    if (user) {
      idToUsername[userId] = user.name;
    }
  }

  return idToUsername;
}
```

For handlers that need access to Node.js features:

```typescript
// Use the Node.js directive at the top of the file
"use node";

import { QueryCtx } from "../../convex/_generated/server";
import * as fs from "fs/promises";

export async function readFileHelper(
  ctx: QueryCtx,
  filePath: string
): Promise<string> {
  // Access Node.js features
  return await fs.readFile(filePath, "utf-8");
}
```

### 4. Add Necessary Database Operations

Use the Convex database API to perform database operations following best practices:

```typescript
// Read operations
const document = await ctx.db.get(id);

// Use indexes for efficient querying (avoid filter)
const documents = await ctx.db
  .query("tableName")
  .withIndex("by_fieldName", (q) => q.eq("fieldName", value))
  .collect();

// Use pagination for large result sets
const paginatedResults = await ctx.db
  .query("tableName")
  .withIndex("by_fieldName", (q) => q.eq("fieldName", value))
  .order("desc")
  .paginate(args.paginationOpts);

// Write operations
const id = await ctx.db.insert("tableName", {
  field1: value1,
  field2: value2,
});

// Use patch for partial updates
await ctx.db.patch(id, { field1: newValue });

// Use replace for full document replacement (careful, this overwrites the entire document)
await ctx.db.replace(id, {
  field1: newValue1,
  field2: newValue2,
  field3: newValue3,
});

// Using search index for text search
const searchResults = await ctx.db
  .query("messages")
  .withSearchIndex("search_body", (q) =>
    q.search("body", searchTerm).eq("channel", channelId)
  )
  .take(10);
```

### 5. Implement Authorization (If Needed)

Use the `withRole` wrapper to enforce role-based access control:

```typescript
withRole(Roles.ADMIN, async (ctx, args) => {
  // Only admins can access this function
});
```

For multiple roles, you can create a custom wrapper:

```typescript
// Custom wrapper for multiple roles
function withMultipleRoles(roles: Roles[], func) {
  return withRole(roles[0], async (ctx, args) => {
    // Check if user has any of the specified roles
    for (const role of roles.slice(1)) {
      if (await userHasRole(ctx, role)) {
        return func(ctx, args);
      }
    }
    return func(ctx, args);
  });
}
```

## Best Practices

1. **Function Organization**: Keep functions organized by domain and feature
2. **Validation**: Always use validators for function arguments and returns
3. **Error Handling**: Use consistent error handling patterns
4. **DTOs**: Define clear DTOs for input and output data
5. **Authorization**: Always apply appropriate authorization using wrappers
6. **Indexes**: Use appropriate database indexes for efficient queries
7. **Documentation**: Document your use cases with inline comments

## Example Use Case: List Users

Below is a real-world example from our admin panel that demonstrates proper Convex function implementation:

```typescript
// convex/functions/useCases/admin/userManagement/users.ts
import {
  mutation,
  MutationCtx,
  QueryCtx,
  query,
} from "../../../../../convex/_generated/server";
import { withRole } from "../../../../../wrappers/auth/withRole";
import { loadUserRelationships } from "../../../../../helpers/auth/userRelationships";
import { Doc } from "../../../../../convex/_generated/dataModel";
import { Roles } from "../../../../../types/enums";
import {
  UsersListRequestDTO,
  UsersListRequestDTOObjectValidator,
  UsersListResponseDTO,
  UsersListResponseDTOValidator,
} from "../../../../../types/dtos";

export const list = query({
  args: UsersListRequestDTOObjectValidator,
  returns: UsersListResponseDTOValidator,
  handler: withRole(
    Roles.ADMIN,
    async (
      ctx: QueryCtx,
      args: UsersListRequestDTO
    ): Promise<UsersListResponseDTO> => {
      // Use search index if search is provided, which also supports role filtering
      let response: Doc<"users">[] = [];

      if (args.search) {
        const users = await ctx.db
          .query("users")
          .withSearchIndex("search_email", (q) => {
            let query = q.search("email", args.search);
            // Apply role filter if roles are specified
            if (args.role && args.role.length > 0) {
              query = query.eq("roles", args.role);
            }
            return query;
          })
          .take(args.limit + (args.offset || 0));

        response = args.offset ? users.slice(args.offset) : users;
      } else if (args.role && args.role.length > 0) {
        // Use regular index for role filtering when no search is provided
        const users = await ctx.db
          .query("users")
          .withIndex("by_roles", (q) => q.eq("roles", args.role))
          .order("desc")
          .take(args.limit + (args.offset || 0));

        response = args.offset ? users.slice(args.offset) : users;
      } else {
        // No filters - use full table scan
        const users = await ctx.db
          .query("users")
          .order("desc")
          .take(args.limit + (args.offset || 0));

        response = args.offset ? users.slice(args.offset) : users;
      }

      // Remove sensitive information from user records
      const usersWithRelationships = await Promise.all(
        response.map(async (user) => {
          const { hashedPassword, providerId, ...userWithoutSensitiveInfo } =
            user;
          return {
            ...userWithoutSensitiveInfo,
            ...(await loadUserRelationships(ctx, user)),
          };
        })
      );

      const total = (await ctx.db.query("users").collect()).length;

      return {
        data: usersWithRelationships,
        total,
      };
    }
  ),
});
```
