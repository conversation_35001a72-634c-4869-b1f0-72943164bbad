"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { useI18n } from "@curatd/shared/locales/client";

import { Button } from "@curatd/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@curatd/ui/components/card";
import { Input } from "@curatd/ui/components/input";

export default function CompleteInvitePage() {
  const t = useI18n();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [sessionSet, setSessionSet] = React.useState(false);
  const [formData, setFormData] = React.useState({
    displayName: "",
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = React.useState<Record<string, string>>({});

  React.useEffect(() => {
    const hash = window.location.hash.substring(1);
    const params = new URLSearchParams(hash);

    const access_token = params.get("access_token");
    const refresh_token = params.get("refresh_token");

    if (!access_token || !refresh_token) {
      alert(t("web.acceptInvitation.invalidToken"));
      router.replace("/login");
      return;
    }

    // TODO: Implement auth

    (async () => {
      const { error } = await supabase.auth.setSession({
        access_token,
        refresh_token,
      });

      if (error) {
        alert(t("web.acceptInvitation.failedToAccept"));
        router.replace("/login");
        return;
      }

      setSessionSet(true);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (formData.password.length < 6) {
      newErrors.password = t("web.acceptInvitation.passwordMinLength");
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = t("web.acceptInvitation.passwordsDoNotMatch");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // TODO: Implement accept invitation with Convex, not supabase
      // const { error } = await supabase.auth.updateUser({
      //   password: formData.password,
      //   data: formData.displayName
      //     ? { display_name: formData.displayName }
      //     : {},
      // });

      // if (error) {
      //   throw error;
      // }

      // alert(t("web.acceptInvitation.success"));
      router.replace("/app");
    } catch (error) {
      console.error("Failed to set password:", error);
      alert(
        error instanceof Error
          ? error.message
          : t("web.acceptInvitation.failedToAccept")
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  if (!sessionSet) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">
            {t("web.acceptInvitation.loading")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="text-2xl font-bold">curatd.</div>
          </div>
          <CardTitle className="text-2xl font-bold">
            {t("web.acceptInvitation.title")}
          </CardTitle>
          <CardDescription>
            {t("web.acceptInvitation.description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="text-sm font-medium">
                {t("web.acceptInvitation.displayNameLabel")}
              </label>
              <Input
                type="text"
                placeholder={t("web.acceptInvitation.displayNamePlaceholder")}
                value={formData.displayName}
                onChange={(e) =>
                  handleInputChange("displayName", e.target.value)
                }
                className="mt-1"
              />
            </div>

            <div>
              <label className="text-sm font-medium">
                {t("web.acceptInvitation.passwordLabel")} *
              </label>
              <Input
                type="password"
                placeholder={t("web.acceptInvitation.passwordPlaceholder")}
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                className="mt-1"
              />
              {errors.password && (
                <p className="text-sm text-red-500 mt-1">{errors.password}</p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium">
                {t("web.acceptInvitation.confirmPasswordLabel")} *
              </label>
              <Input
                type="password"
                placeholder={t(
                  "web.acceptInvitation.confirmPasswordPlaceholder"
                )}
                value={formData.confirmPassword}
                onChange={(e) =>
                  handleInputChange("confirmPassword", e.target.value)
                }
                className="mt-1"
              />
              {errors.confirmPassword && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.confirmPassword}
                </p>
              )}
            </div>

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting
                ? t("web.acceptInvitation.accepting")
                : t("web.acceptInvitation.acceptButton")}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
