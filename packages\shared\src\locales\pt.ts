export default {
    admin: {
        nav: {
            dashboard: 'Dashboard',
            users: 'Usu<PERSON><PERSON><PERSON>',
            join: 'Entrar',
        },
        user: {
            account: '<PERSON><PERSON> conta',
            logout: 'Sair',
        },
        userManagement: {
            title: 'Gestão de Usuários',
            description: 'Gerenciar contas de usuário, funções e permissões',
            inviteUser: 'Convidar Usuário',
            search: 'Pesquisar usuários...',
            allRoles: 'Todas as funções',
            allStatuses: 'Todos os status',
            active: 'Ativo',
            inactive: 'Inativo',
            invitationPending: 'Convite Pendente',
            invitationExpired: 'Convite <PERSON>pirado',
            invitationCancelled: 'Convite Cancelado',
            invitationAccepted: 'Convite <PERSON>',
            filters: 'filtro',
            filters_plural: 'filtros',
            applied: 'aplicado',
            applied_plural: 'aplicados',
            clear: 'Limpar',
            noFiltersApplied: 'Nenhum filtro aplicado',
            totalUsers: 'usuários no total',
            columns: 'Colunas',
            loadingUsers: 'Carregando usuários...',
            noUsersFound: 'Nenhum usuário encontrado.',
            failedToLoad: 'Falha ao carregar usuários',
            retry: 'Tentar novamente',
            user: 'Usuário',
            roles: 'Funções',
            noRoles: 'Sem funções',
            status: 'Status',
            created: 'Criado',
            actions: 'Ações',
            editProfile: 'Editar Perfil',
            manageRoles: 'Gerenciar Funções',
            resendInvitation: 'Reenviar Convite',
            activate: 'Ativar',
            deactivate: 'Desativar',
            delete: 'Excluir',
            userActivated: 'Usuário ativado com sucesso',
            userDeactivated: 'Usuário desativado com sucesso',
            userDeleted: 'Usuário excluído com sucesso',
            failedToUpdateStatus: 'Falha ao atualizar status do usuário',
            failedToDelete: 'Falha ao excluir usuário',
            confirmDelete: 'Tem certeza que deseja excluir este usuário?',
            rowsPerPage: 'Linhas por página',
            page: 'Página',
            of: 'de',
            goToFirstPage: 'Ir para primeira página',
            goToPreviousPage: 'Ir para página anterior',
            goToNextPage: 'Ir para próxima página',
            goToLastPage: 'Ir para última página',
            inviteDialog: {
                title: 'Convidar Usuário',
                description: 'Enviar um convite para um novo usuário. Ele receberá um email com instruções para configurar sua conta.',
                emailLabel: 'Endereço de Email',
                emailPlaceholder: '<EMAIL>',
                displayNameLabel: 'Nome de Exibição (Opcional)',
                displayNamePlaceholder: 'João Silva',
                rolesLabel: 'Funções',
                selectedRoles: 'Funções selecionadas:',
                loadingRoles: 'Carregando funções...',
                cancel: 'Cancelar',
                sending: 'Enviando...',
                sendInvitation: 'Enviar Convite',
                invitationSent: 'Convite de usuário enviado com sucesso',
                invitationFailed: 'Falha ao enviar convite',
                pleaseEnterValidEmail: 'Por favor, digite um endereço de email válido',
                pleaseSelectAtLeastOneRole: 'Por favor, selecione pelo menos uma função',
                errors: {
                    userAlreadyExists: 'Um usuário com este endereço de email já existe',
                    invalidEmail: 'Por favor, digite um endereço de email válido',
                    missingRoles: 'Por favor, selecione pelo menos uma função',
                    networkError: 'Erro de rede. Verifique sua conexão e tente novamente',
                    serverError: 'Erro do servidor. Tente novamente mais tarde',
                    authenticationRequired: 'Autenticação necessária. Faça login novamente',
                    insufficientPermissions: 'Você não tem permissão para convidar usuários',
                    invalidRoles: 'Uma ou mais funções selecionadas são inválidas',
                    emailConfigurationError: 'Serviço de email não está configurado corretamente. Entre em contato com o suporte',
                    configurationError: 'Erro de configuração do sistema. Entre em contato com o suporte',
                    genericError: 'Ocorreu um erro inesperado. Tente novamente',
                    validationError: 'Verifique sua entrada e tente novamente',
                    supabaseError: 'Serviço temporariamente indisponível. Tente novamente mais tarde',
                    notFound: 'O recurso solicitado não foi encontrado',
                    rateLimitExceeded: 'Muitas solicitações. Aguarde um momento e tente novamente',
                },
            },
            manageRolesDialog: {
                title: 'Gerenciar funções para {userName}',
                description: 'Atribuir ou remover funções para este usuário.',
                cancel: 'Cancelar',
                saving: 'Salvando...',
                saveChanges: 'Salvar alterações',
                successMessage: 'Funções atualizadas com sucesso.',
                errorMessage: 'Ocorreu um erro ao atualizar as funções.'
            },
            bulkActions: {
                confirmDeactivation: 'Tem certeza que deseja desativar {count} usuário?',
                confirmDeactivation_plural: 'Tem certeza que deseja desativar {count} usuários?',
                deactivationSuccess: '{count} usuário foi desativado com sucesso.',
                deactivationSuccess_plural: '{count} usuários foram desativados com sucesso.',
                deactivationError: 'Falha ao desativar usuários.',
                selected: '{count} de {total} linha selecionada.',
                selected_plural: '{count} de {total} linhas selecionadas.',
                editRoles: 'Editar Funções',
                delete: 'Excluir'
            },
            bulkManageRolesDialog: {
                title: 'Editar Funções em Massa para {count} Usuário',
                title_plural: 'Editar Funções em Massa para {count} Usuários',
                description: 'Isso ADICIONARÁ as funções selecionadas a todos os usuários selecionados. Não removerá suas funções existentes.',
                assignButton: 'Atribuir Funções',
                noRoleSelectedError: 'Por favor, selecione pelo menos uma função para atribuir.',
                successMessage: 'Funções atribuídas com sucesso a {count} usuário.',
                successMessage_plural: 'Funções atribuídas com sucesso a {count} usuários.',
                errorMessage: 'Ocorreu um erro ao atualizar funções em massa.'
            },
            stripeStatus: 'Status do Stripe',
            stripeStatuses: {
                onboarding: 'Em integração',
                active: 'Ativo',
                inactive: 'Inativo',
                restricted: 'Restrito',
                not_connected: 'Não conectado',
            },
            profileTypes: {
                coach: 'Treinador',
                facilityManager: 'Gerente de Instalação',
            },
        },
        acceptInvitation: {
            title: 'Finalizar Configuração da Conta',
            description: 'Configure sua senha para acessar o painel administrativo',
            displayNameLabel: 'Nome de Exibição',
            displayNamePlaceholder: 'Digite seu nome completo',
            passwordLabel: 'Senha',
            passwordPlaceholder: 'Digite sua senha',
            confirmPasswordLabel: 'Confirmar Senha',
            confirmPasswordPlaceholder: 'Confirme sua senha',
            acceptButton: 'Finalizar Configuração',
            accepting: 'Configurando conta...',
            loading: 'Carregando...',
            success: 'Configuração da conta concluída! Redirecionando para o painel...',
            invalidToken: 'Link de convite inválido ou expirado',
            failedToAccept: 'Falha ao finalizar configuração da conta',
            passwordMinLength: 'A senha deve ter pelo menos 6 caracteres',
            passwordsDoNotMatch: 'As senhas não coincidem',
        },
        unauthorized: {
            title: 'Acesso Negado',
            description: 'Você não tem permissão para acessar o painel administrativo. Esta área é restrita apenas a administradores da plataforma.',
            needAccess: 'Precisa de acesso?',
            contactAdmin: 'Entre em contato com um administrador da plataforma para solicitar acesso ao painel administrativo.',
            returnToMain: 'Voltar ao Site Principal',
        }
    },
    user: {
        default_name: 'Usuário',
    },
    web: {
        acceptInvitation: {
            title: 'Entrar no Curatd',
            description: 'Finalize a configuração da sua conta para começar a reservar treinos',
            displayNameLabel: 'Nome de Exibição',
            displayNamePlaceholder: 'Digite seu nome completo',
            passwordLabel: 'Senha',
            passwordPlaceholder: 'Digite sua senha',
            confirmPasswordLabel: 'Confirmar Senha',
            confirmPasswordPlaceholder: 'Confirme sua senha',
            acceptButton: 'Entrar Agora',
            accepting: 'Configurando conta...',
            loading: 'Carregando...',
            success: 'Bem-vindo ao Curatd! Redirecionando para seu painel...',
            invalidToken: 'Link de convite inválido ou expirado',
            failedToAccept: 'Falha ao finalizar configuração da conta',
            passwordMinLength: 'A senha deve ter pelo menos 6 caracteres',
            passwordsDoNotMatch: 'As senhas não coincidem',
        },
    },
    onboarding: {
        title: 'Bem-vindo ao Curatd',
        subtitle: 'Escolha seu papel para começar sua jornada fitness',
        description: 'Selecione a opção que melhor te descreve para personalizar sua experiência',
        roleSelection: {
            title: 'O que melhor te descreve?',
            subtitle: 'Isso nos ajuda a personalizar sua experiência',
            regularUser: {
                title: 'Estou aqui para treinar',
                description: 'Reserve treinos, encontre treinadores e alcance seus objetivos fitness',
                features: {
                    bookWorkouts: 'Reserve treinos instantaneamente',
                    findCertifiedTrainers: 'Encontre treinadores certificados',
                    trackProgress: 'Acompanhe seu progresso',
                    joinGroupSessions: 'Participe de sessões em grupo'
                }
            },
            coach: {
                title: 'Sou um treinador',
                description: 'Compartilhe sua expertise e ajude outros a alcançar seus objetivos fitness',
                features: {
                    createTrainingPrograms: 'Crie programas de treinamento',
                    manageSchedule: 'Gerencie sua agenda',
                    acceptPayments: 'Aceite pagamentos',
                    buildClientBase: 'Construa sua base de clientes'
                }
            },
            facilityManager: {
                title: 'Gerencio uma instalação',
                description: 'Liste seu espaço e conecte-se com treinadores e atletas',
                features: {
                    listFacility: 'Liste sua instalação',
                    manageBookings: 'Gerencie reservas',
                    partnerWithTrainers: 'Faça parcerias com treinadores',
                    earnRevenue: 'Gere receita'
                }
            },
            buttons: {
                continue: 'Continuar',
                getStarted: 'Começar',
                back: 'Voltar',
                skip: 'Pular por agora',
                selectRole: 'Selecionar este papel',
                selected: 'Selecionado'
            },
            steps: {
                roleSelection: 'Seleção de Papel',
                profileSetup: 'Configuração do Perfil',
                preferences: 'Preferências',
                complete: 'Completo'
            },
            completion: {
                title: 'Você está pronto!',
                subtitle: 'Bem-vindo à sua experiência fitness personalizada',
                description: 'Comece a explorar tudo que o Curatd tem a oferecer',
                cta: 'Começar a Explorar'
            }
        },
        home: {
            title: 'Início',
            description: 'Página inicial'
        }
    },
    nav: {
        brand: 'curatd.',
        athlete: 'Para Atletas',
        coach: 'Para Coaches',
        gym: 'Para Academias',
        contact: 'Contato',
        language: 'Idioma',
        openMenu: 'Abrir menu',
        closeMenu: 'Fechar menu'
    },
    landing: {
        hero: {
            title: 'Bem-estar privado. Onde quiser, quando quiser.',
            subtitle: 'Marque sessões personalizadas em casa, ao ar livre ou em espaços parceiros selecionados.',
            cta: 'Registe-se agora'
        },
        carousel: {
            title: 'Sessões disponíveis',
            items: {
                strength: {
                    title: 'Força',
                    subtitle: 'Ganhe músculo, potência e confiança'
                },
                cardio: {
                    title: 'Cardio',
                    subtitle: 'Aumente a resistência e queime calorias'
                },
                flow: {
                    title: 'Yoga',
                    subtitle: 'Equilíbrio entre corpo e mente (yoga, pilates…)'
                },
                relax: {
                    title: 'Relaxar',
                    subtitle: 'Recupere com massagens e bem-estar'
                }
            }
        },
        faq: {
            title: 'Perguntas Frequentes',
            subtitle: 'Tem dúvidas? Nós temos as respostas. Encontre tudo que precisa saber sobre reservar seus treinos.',
            contactCta: 'Contatar Suporte',
            contactSubtitle: 'Ainda tem dúvidas?',
            questions: {
                inclusivity: {
                    question: 'O Curatd é adequado para idosos, mulheres grávidas, pessoas com deficiência ou aqueles que treinam em grupo?',
                    answer: 'Absolutamente! O Curatd é projetado para todos. Nossos treinadores certificados são especialmente treinados para adaptar as sessões a todos os perfis e necessidades - seja você idoso, grávida, com deficiência, ou queira treinar com amigos ou família. Acreditamos que o fitness deve ser acessível a todos, e cada treinador personaliza os treinos para garantir uma experiência segura, eficaz e agradável para cada indivíduo.'
                },
                howItWorks: {
                    question: 'Como funciona o sistema de reservas?',
                    answer: 'Nosso sistema de reservas é projetado para ser simples e rápido. Apenas navegue pelos treinos disponíveis, selecione seu horário preferido e confirme sua reserva em menos de 30 segundos. Você receberá confirmação instantânea com todos os detalhes necessários.'
                },
                locations: {
                    question: 'Onde posso reservar treinos?',
                    answer: 'Você pode reservar treinos em qualquer lugar - em casa, ao ar livre em parques, ou em academias parceiras. Seja viajando a negócios ou ficando local, nossa plataforma conecta você com treinadores certificados em todas as cidades onde operamos.'
                },
                workoutTypes: {
                    question: 'Que tipos de treinos estão disponíveis?',
                    answer: 'Oferecemos treinamento de força, cardio, yoga, pilates, HIIT, boxe, fitness dance, e muito mais. Seja você iniciante ou atleta avançado, cada treinador é preparado para adaptar as sessões ao seu nível, idade e circunstâncias especiais.'
                },
                equipment: {
                    question: 'Preciso trazer algum equipamento?',
                    answer: 'Nenhum equipamento necessário! Nossos treinadores vêm totalmente equipados com tudo necessário para seu treino. Para sessões na academia, você terá acesso a todos os equipamentos, e treinos ao ar livre são projetados para usar equipamento mínimo ou nenhum.'
                },
                companions: {
                    question: 'Posso trazer alguém para meu treino?',
                    answer: 'Absolutamente! Trazer um amigo, parceiro ou membro da família é encorajado. Treinar junto pode ser mais motivador e divertido. Apenas nos avise ao reservar para que possamos garantir que a sessão acomode todos.'
                },
                cancellation: {
                    question: 'Qual é sua política de cancelamento e reserva?',
                    answer: 'Você pode reservar com até 30 dias de antecedência e cancelar ou reagendar até 24 horas antes da sua sessão sem penalidade. Cancelamentos dentro de 24 horas podem incorrer em uma pequena taxa dependendo da política do treinador.'
                },
                pricing: {
                    question: 'Quanto custam as sessões?',
                    answer: 'As sessões começam a partir de R$45 por aula no pagamento individual. Com nosso plano de assinatura, você pode economizar até 40% e pagar apenas R$27 por sessão, além de ter acesso prioritário aos seus treinadores favoritos.'
                }
            }
        },
        howItWorks: {
            title: 'Como Funciona',
            subtitle: '4 passos para seu próximo treino',
            cta: 'Registe-se',
            steps: {
                selectSportDuration: {
                    title: 'Crie o seu perfil e explore sessões à sua medida perto de si.',
                    description: 'Escolha a sua sessão',
                },
                chooseCoach: {
                    title: 'Selecione a atividade, hora, local e professor.',
                    description: 'Personalize e reserve',
                },
                bookLocation: {
                    title: 'Personalize e reserve',
                    description: 'Adicione um amigo, escolha as opções e reserve com um clique.',
                },
                payAndGo: {
                    title: 'Mova-se',
                    description: 'Encontre o seu professor e aproveite a sessão — onde quer que esteja.',
                }
            }
        },
        places: {
            title: 'Treine',
            titleHighlight: 'Em Qualquer Lugar',
            subtitle: 'Quer esteja em casa, em viagem de negócios ou de lazer — tenha acesso a sessões privadas ao ar livre ou em espaços parceiros.',
            items: {
                atHome: {
                    title: 'Vamos até si.',
                    subtitle: 'Em casa',
                    description: 'Treinos privados no conforto do seu lar.',
                    features: {
                        comfort: 'Máximo conforto e conveniência',
                        flexibility: 'Treine no seu próprio horário',
                        privacy: 'Traga um amigo ou treine sozinho'
                    }
                },
                publicSpaces: {
                    title: 'O mundo é o seu ginásio.',
                    subtitle: 'Ao ar livre',
                    description: 'Jardins, praias, miradouros',
                    features: {
                        travel: 'Viagens de negócios e férias fitness',
                        global: 'Treinadores locais nas principais cidades',
                        flexible: 'Academias de hotel, parques ou ao ar livre'
                    }
                },
                partnerFacilities: {
                    title: 'Damos-lhe acesso a espaços de treino exclusivos.',
                    subtitle: 'Espaços parceiros',
                    description: 'Treine em hotéis, ginásios ou espaços partilhados.',
                    features: {
                        equipment: 'Todo equipamento fornecido',
                        community: 'Sozinho ou acompanhado',
                        professional: 'Supervisão especializada disponível'
                    }
                }
            }
        },
        subscription: {
            title: 'Desbloqueie Mais',
            titleHighlight: 'Economize Mais',
            subtitle: 'Pague por sessão ou aproveite descontos e benefícios exclusivos com a nossa subscrição mensal.',
            payPerClass: {
                title: 'Sessão avulsa',
                price: 'A partir de 50 €',
                priceUnit: 'Na sessão',
                description: 'Perfeito para quem não quer compromissos.',
                features: {
                    access: 'Totalmente flexível, sem compromissos',
                    booking: 'Reserve o que quiser, quando quiser',
                    flexibility: 'Liberdade total',
                },
                cta: 'Inscrição'
            },
            subscription: {
                title: 'Curatd+',
                price: '19.99€',
                priceUnit: 'al mês',
                description: 'Uma forma inteligente de manter a forma e poupar dinheiro.',
                features: {
                    discount: '15 % de desconto em todas as reservas',
                    priority: 'Acesso prioritário',
                    exclusive: 'Equipamento incluído',
                    invites: 'Eventos exclusivos'
                },
                cta: 'Assinatura'
            },
            learnMore: 'Saiba Mais',
            benefits: {
                smartSavings: {
                    title: 'Economia Inteligente',
                    description: 'Quanto mais você treina, mais economiza com nosso modelo de assinatura'
                },
                priorityAccess: {
                    title: 'Acesso Prioritário',
                    description: 'Reserve seus treinadores favoritos e horários antes de todos os outros'
                },
                noCommitment: {
                    title: 'Sem Compromisso',
                    description: 'Pause ou cancele a qualquer momento com flexibilidade completa'
                }
            }
        },
        contact: {
            title: 'Entre em Contato',
            subtitle: 'Tem dúvidas ou quer saber mais? Adoraríamos ouvir de você. Envie-nos uma mensagem e retornaremos o mais breve possível.',
            getInTouch: 'Entre em Contato',
            email: '<EMAIL>',
            phone: '+****************',
            address: '123 Rua do Fitness, Cidade Saúde, CS 12345',
            followUs: 'Siga-nos',
            sendMessage: 'Enviar Mensagem',
            namePlaceholder: 'Seu Nome',
            emailPlaceholder: '<EMAIL>',
            subjectPlaceholder: 'Assunto',
            messagePlaceholder: 'Conte-nos sobre sua dúvida ou como podemos ajudá-lo...',
            sending: 'Enviando...'
        },
        joinNow: {
            title: 'Entre na Lista de Espera',
            subtitle: 'Seja dos primeiros a experimentar a Curatd.',
            formTitle: 'Acesso antecipado e benefícios exclusivos.',
            emailPlaceholder: 'Digite seu endereço de email',
            submitButton: 'Entrar na Lista',
            submitting: 'Entrando...',
            disclaimer: 'Respeitamos sua privacidade e não enviaremos spam. Cancele a inscrição a qualquer momento.',
            successTitle: 'Você está na lista!',
            successMessage: 'Obrigado por entrar na nossa lista de espera. Entraremos em contato em breve com atualizações exclusivas.',
            benefits: {
                earlyAccess: {
                    title: 'Acesso Antecipado',
                    description: 'Seja um dos primeiros a testar nossa plataforma antes do lançamento público'
                },
                specialOffer: {
                    title: 'Ofertas Especiais',
                    description: 'Obtenha descontos exclusivos e tarifas promocionais para usuários pioneiros'
                },
                updates: {
                    title: 'Últimas Atualizações',
                    description: 'Mantenha-se informado sobre novos recursos, treinadores e locais'
                }
            }
        },
        coach: {
            hero: {
                title: 'Ganhe mais alunos. Faça crescer o seu negócio — ao seu ritmo.',
                subtitle: 'unte-se a uma plataforma premium feita para treinadores de topo. Defina o seu horário, trabalhe com flexibilidade e aumente os seus rendimentos.',
                cta: 'Candidate-se para ser Coach Curatd.'
            },
            benefits: {
                title: 'Por que se juntar à nossa plataforma de coaching?',
                items: {
                    flexibility: {
                        badge: 'Flexibilidade & Liberdade',
                        title: 'Flexibilidade total',
                        subtitle: 'Defina o seu horário',
                        description: 'Treine onde e quando quiser'
                    },
                    travelWork: {
                        badge: 'Viagem & Trabalho',
                        title: 'Trabalhe de onde quiser',
                        subtitle: 'O nosso modelo permite dar aulas em qualquer cidade onde operamos',
                        description: 'Viaje e ganhe — com uma plataforma segura ao seu lado'
                    },
                    expertise: {
                        badge: 'Clientes Qualificados & Rendimentos',
                        title: 'Trazemos-lhe os clientes certos',
                        subtitle: 'Trabalhe com pessoas motivadas e comprometidas',
                        description: 'Tarifas competitivas e bónus por desempenho'
                    },
                    growth: {
                        badge: 'Visibilidade & Crescimento',
                        title: 'Cuidamos do marketing e das marcações',
                        subtitle: 'Foque-se nas aulas',
                        description: 'Expanda a sua marca pessoal e chegue a mais pessoas'
                    }
                }
            },
            howItWorks: {
                title: 'Como funciona',
                steps: {
                    signUp: {
                        title: 'Inscreva-se',
                        description: 'Indique a sua localização, modalidade, experiência e disponibilidade.',
                    },
                    getVerified: {
                        title: 'Seja verificado',
                        description: 'Analisamos o seu perfil e qualificações. Se for aprovado, ajudamos a configurar o seu perfil e onboarding.',
                    },
                    startCoaching: {
                        title: 'Comece a treinar',
                        description: 'Defina o seu horário e preços. Comece a receber alunos. Ganhe ao seu ritmo.'
                    }
                }
            },
            signup: {
                title: 'Pronto para se juntar à equipa Curatd?',
                subtitle: 'Preencha o formulário e entraremos em contacto consigo.',
                formTitle: 'Entrar como Coach',
                formDescription: 'Comece sua jornada com nossa plataforma de coaching',
                form: {
                    fullName: {
                        label: 'Nome completo (opcional)',
                        placeholder: 'Digite seu nome completo'
                    },
                    email: {
                        label: 'Endereço de email',
                        placeholder: 'Digite seu endereço de email',
                        required: true
                    },
                    sportTypes: {
                        label: 'Tipo(s) de esporte que você treina',
                        placeholder: 'ex: Tênis, Basketball, Natação, Treinamento Personal...',
                        required: true
                    }
                },
                cta: 'Entrar agora',
                submitting: 'Enviando...',
                privacyNote: 'Respeitamos sua privacidade. Suas informações são usadas apenas para contatá-lo sobre ingressar em nossa plataforma.',
                success: 'Obrigado! Entraremos em contato em breve para começar.',
                errors: {
                    fillRequired: 'Por favor, preencha todos os campos obrigatórios',
                    tryAgain: 'Algo deu errado. Tente novamente.'
                }
            }
        },
        gym: {
            hero: {
                title: 'Traga mais pessoas para sua academia. Aumente sua receita.',
                subtitle: 'Junte-se à nossa plataforma e deixe entusiastas do esporte motivados treinar em sua academia. Cada visita através da nossa plataforma lhe rende dinheiro.',
                cta: 'Registre sua academia'
            },
            benefits: {
                title: 'Por que se juntar à nossa rede?',
                items: {
                    fillYourGym: {
                        badge: 'Monetise Unused Hours',
                        title: 'Rentabilize as horas mortas',
                        description: 'Disponibilize o seu espaço em horários específicos.',
                        subtitle: 'Ganhe sem custos operacionais adicionais'
                    },
                    earnEveryTime: {
                        badge: 'Premium Exposure',
                        title: 'Atraia clientes premium e exigentes',
                        description: 'Faça parte de uma rede curada de parceiros',
                        subtitle: 'Aumente a visibilidade local e digital'
                    },
                    freePromotion: {
                        badge: 'Easy Integration',
                        title: 'Sem contratos complicados',
                        description: 'Agendamento flexível via plataforma Curatd',
                        subtitle: 'Cuidamos das marcações, pagamentos e suporte'
                    },
                    simpleFlexible: {
                        badge: 'Trusted Network',
                        title: 'Trabalhe apenas com treinadores verificados e sessões asseguradas',
                        description: 'Todos os clientes são previamente avaliados',
                        subtitle: 'Uma comunidade segura e profissional'
                    }
                }
            },
            howItWorks: {
                title: 'Como funciona',
                steps: {
                    registerGym: {
                        title: 'Preencha o formulário com seus detalhes.',
                        description: '1. Partilhe o seu endereço, tipo de espaço e horários disponíveis.'
                    },
                    review: {
                        title: 'Publicamos sua academia em nossa plataforma.',
                        description: '2. Nós examinamos o seu perfil e verificamos o espaço.'
                    },
                    welcomeUsers: {
                        title: 'As pessoas reservam e vêm treinar, e você é pago por cada visita.',
                        description: '3. Ganhe dinheiro a cada visita.'
                    }
                }
            },
            callToAction: {
                title: 'Pronto para transformar sua academia?',
                subtitle: 'Junte-se ao futuro da gestão do esporte. Vamos discutir como o Curatd pode ajudar sua academia a prosperar.',
                formTitle: 'Registre sua academia hoje',
                formDescription: 'Pronto para trazer mais pessoas para sua academia e fazer seu negócio crescer? Preencha o formulário abaixo — leva apenas 2 minutos.',
                form: {
                    gymName: {
                        label: 'Nome da academia',
                        placeholder: 'Digite o nome da sua academia',
                        required: true
                    },
                    email: {
                        label: 'Endereço de email',
                        placeholder: 'Digite seu endereço de email',
                        required: true
                    },
                    address: {
                        label: 'Endereço/localização da academia',
                        placeholder: 'Digite o endereço da sua academia',
                        required: true
                    },
                    phone: {
                        label: 'Número de telefone',
                        placeholder: 'Digite seu número de telefone',
                        required: false
                    },
                    message: {
                        label: 'Mensagem ou descrição',
                        placeholder: 'Conte-nos sobre sua academia...',
                        required: false
                    }
                },
                cta: 'Registrar agora',
                privacyNote: 'Respeitamos sua privacidade. Suas informações são usadas apenas para contatá-lo sobre ingressar em nossa plataforma.'
            }
        },
        footer: {
            brand: {
                title: 'curatd.',
                description: 'Sessões Privadas'
            },
            quickLinks: {
                title: 'Links Rápidos',
                forAthletes: 'Para Atletas',
                forCoaches: 'Para Treinadores',
                forGyms: 'Para Academias',
                contact: 'Contato'
            },
            legal: {
                title: 'Legal',
                privacyPolicy: 'Política de Privacidade',
                termsOfService: 'Termos de Serviço'
            },
            bottomBar: {
                copyright: ' Curatd. Todos os direitos reservados.',
                madeBy: 'Feito com <3 por The Tech Nation'
            }
        },
        preRegistration: {
            errors: {
                fillRequired: 'Por favor, preencha todos os campos obrigatórios',
                emailRequired: 'Por favor, digite seu endereço de email'
            },
            submitting: 'Enviando...',
            user: {
                email: {
                    placeholder: 'Digite seu endereço de email',
                },
                cta: 'Entrar agora',
                submitting: 'Enviando...',
                privacyNote: 'Respeitamos sua privacidade. Suas informações são usadas apenas para contatá-lo sobre ingressar em nossa plataforma.',
                successTitle: 'Obrigado! Entraremos em contato em breve para começar.',
                successMessage: 'Obrigado por entrar na nossa lista de espera. Entraremos em contato em breve com atualizações exclusivas.',
                duplicateTitle: 'Já registrado!',
                duplicateMessage: 'Você já está na lista! Entraremos em contato em breve para começar.',
            },
            coach: {
                fullName: {
                    label: 'Nome completo (opcional)',
                    placeholder: 'Digite seu nome completo'
                },
                email: {
                    label: 'Endereço de email',
                    placeholder: 'Digite seu endereço de email',
                },
                sportTypes: {
                    label: 'Tipo(s) de esporte que você treina',
                    placeholder: 'ex: Tênis, Basketball, Natação, Treinamento Personal...',
                },
                cta: 'Entrar agora',
                submitting: 'Enviando...',
                privacyNote: 'Respeitamos sua privacidade. Suas informações são usadas apenas para contatá-lo sobre ingressar em nossa plataforma.',
                successTitle: 'Obrigado! Entraremos em contato em breve para começar.',
                duplicateTitle: 'Já registrado!',
                duplicateMessage: 'Você já está na lista! Entraremos em contato em breve para começar.',
            },
            gym: {
                gymName: {
                    label: 'Nome da academia',
                    placeholder: 'Digite o nome da sua academia',
                },
                email: {
                    label: 'Endereço de email',
                    placeholder: 'Digite seu endereço de email',
                },
                address: {
                    label: 'Endereço/localização da academia',
                    placeholder: 'Digite o endereço da sua academia',
                },
                phone: {
                    label: 'Número de telefone',
                    placeholder: 'Digite seu número de telefone',
                },
                message: {
                    label: 'Mensagem ou descrição',
                    placeholder: 'Conte-nos sobre sua academia...',
                },
                cta: 'Registrar agora',
                submitting: 'Enviando...',
                privacyNote: 'Respeitamos sua privacidade. Suas informações são usadas apenas para contatá-lo sobre ingressar em nossa plataforma.',
                successTitle: 'Obrigado! Entraremos em contato em breve para começar.',
            }
        },
    },
    privacy: {
        title: 'Política de Privacidade',
        lastUpdated: 'Última atualização',
        sections: {
            introduction: {
                title: 'Introdução',
                content: 'Bem-vindo ao Curatd. Respeitamos sua privacidade e estamos comprometidos em proteger seus dados pessoais. Esta política de privacidade informará como cuidamos de seus dados pessoais quando você visita nosso site e informará sobre seus direitos de privacidade e como a lei o protege.'
            },
            dataCollection: {
                title: 'Quais Dados Coletamos',
                content: 'Podemos coletar, usar, armazenar e transferir diferentes tipos de dados pessoais sobre você que agrupamos da seguinte forma:',
                items: [
                    'Dados de Identidade: inclui nome, sobrenome de solteira, sobrenome, nome de usuário ou identificador similar, estado civil, título, data de nascimento e gênero.',
                    'Dados de Contato: inclui endereço de cobrança, endereço de entrega, endereço de e-mail e números de telefone.',
                    'Dados Técnicos: inclui endereço de protocolo de internet (IP), seus dados de login, tipo e versão do navegador, configuração de fuso horário e localização, tipos e versões de plug-ins do navegador, sistema operacional e plataforma.',
                    'Dados de Perfil: inclui seu nome de usuário e senha, compras ou pedidos feitos por você, seus interesses, preferências, feedback e respostas de pesquisa.',
                    'Dados de Uso: inclui informações sobre como você usa nosso site, produtos e serviços.',
                    'Dados de Marketing e Comunicações: inclui suas preferências para receber marketing nosso e de terceiros e suas preferências de comunicação.'
                ]
            },
            dataUse: {
                title: 'Como Usamos Seus Dados',
                content: 'Só usaremos seus dados pessoais quando a lei nos permitir. Mais comumente, usaremos seus dados pessoais nas seguintes circunstâncias:',
                items: [
                    'Para fornecer e manter nosso serviço',
                    'Para notificá-lo sobre mudanças em nosso serviço',
                    'Para permitir que você participe de recursos interativos de nosso serviço quando escolher fazê-lo',
                    'Para fornecer atendimento ao cliente e suporte',
                    'Para fornecer análise ou informações valiosas para que possamos melhorar o serviço',
                    'Para monitorar o uso do serviço',
                    'Para detectar, prevenir e resolver problemas técnicos'
                ]
            },
            dataSharing: {
                title: 'Compartilhamento de Dados',
                content: 'Não vendemos, negociamos ou transferimos seus dados pessoais para terceiros sem seu consentimento, exceto conforme descrito nesta política. Podemos compartilhar suas informações com:',
                items: [
                    'Prestadores de serviços que nos ajudam a operar nosso site e conduzir nossos negócios',
                    'Consultores profissionais, incluindo advogados, banqueiros, auditores e seguradoras',
                    'Órgãos governamentais que nos exigem relatar atividades de processamento'
                ]
            },
            dataSecurity: {
                title: 'Segurança de Dados',
                content: 'Implementamos medidas de segurança apropriadas para evitar que seus dados pessoais sejam perdidos acidentalmente, usados ou acessados de forma não autorizada, alterados ou divulgados. Limitamos o acesso aos seus dados pessoais a funcionários, agentes, contratados e outros terceiros que tenham necessidade comercial de saber.'
            },
            yourRights: {
                title: 'Seus Direitos',
                content: 'Sob certas circunstâncias, você tem direitos sob as leis de proteção de dados em relação aos seus dados pessoais:',
                items: [
                    'Solicitar acesso aos seus dados pessoais',
                    'Solicitar correção dos seus dados pessoais',
                    'Solicitar apagamento dos seus dados pessoais',
                    'Objetar ao processamento dos seus dados pessoais',
                    'Solicitar restrição do processamento dos seus dados pessoais',
                    'Solicitar transferência dos seus dados pessoais',
                    'Direito de retirar consentimento'
                ]
            },
            contact: {
                title: 'Entre em Contato',
                content: 'Se você tiver alguma dúvida sobre esta Política de Privacidade, entre em contato conosco por e-<NAME_EMAIL> ou através do nosso formulário de contato no site.'
            }
        },
        backToHome: 'Voltar ao Início'
    },
    login: {
        title: "Bem-vindo(a) de volta!",
        description: "Faça login em sua conta para continuar!",
        subdescription: "Ainda não tem uma conta? Ela será criada automaticamente!",
        admin: {
            title: "Login Admin",
            description: "Faça login no painel administrativo",
            subdescription: "Acesso administrativo apenas - contate o suporte se precisar de acesso",
        },
        email: {
            label: "Email",
            placeholder: "Digite seu email",
            send_code: "Enviar código de verificação",
            sending: "Enviando código...",
        },
        otp: {
            title: "Verifique seu email",
            description: "Enviamos um código de verificação para",
            label: "Código de verificação",
            verify: "Verificar código",
            verifying: "Verificando...",
            back: "Voltar ao email",
        },
        social: {
            continue_with: "Ou continue com",
        },
        errors: {
            generic: "Ocorreu um erro",
            invalid_email: "Por favor, digite um endereço de email válido",
            incomplete_otp: "Por favor, digite o código completo de 6 dígitos",
            send_otp_failed: "Falha ao enviar código",
            verify_otp_failed: "Código de verificação inválido",
        },
        loading: "Fazendo login...",
    },
    logout: {
        title: "Sair",
        description: "Tem certeza que deseja sair?",
        confirm: "Sair",
        cancel: "Cancelar",
    },
    business: {
        nav: {
            dashboard: 'Dashboard',
            generalProfile: 'Perfil Geral',
            otherPage: 'Outra Página',
            switchEntity: 'Trocar Entidade',
            coach: 'Treinador',
            facilityManager: 'Gerente de Instalação',
        },
        entityDisplay: {
            coach: 'Perfil Treinador',
            facilityManager: 'Perfil Gerente de Instalação',
            id: 'ID: {id}',
        },
    },
    errors: {
        404: {
            title: "Ops! Página não encontrada",
            subtitle: "A página que você está procurando parece ter saído para sua própria jornada fitness.",
            description: "A página que você está procurando parece ter saído para sua própria jornada fitness. Vamos colocá-lo de volta no caminho certo!",
            goHome: "Ir para o início",
            goBack: "Voltar",
            lookingFor: "Procurando por algo específico?",
            athleteDesc: "Reservar sessões de treinamento pessoal",
            coachDesc: "Junte-se e faça crescer seu negócio",
            gymDesc: "Seja nosso parceiro",
            startTraining: "Começar treinamento",
            becomeCoach: "Tornar-se treinador",
            partnerNow: "Ser parceiro agora",
        }
    },
} as const; 