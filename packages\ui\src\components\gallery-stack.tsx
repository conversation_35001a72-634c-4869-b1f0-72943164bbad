import { useState, useEffect, useRef } from "react";
import { motion, useTransform, useScroll, AnimatePresence } from "motion/react";
import { useIsMobile } from "@curatd/ui/hooks/use-mobile";

interface GalleryStackProps {
    images: { src: string }[];
}

const GalleryStack = ({ images }: GalleryStackProps) => {
    const [activeImage, setActiveImage] = useState<number | null>(0);
    const containerRef = useRef<HTMLDivElement>(null);
    const isMobile = useIsMobile();

    const displayImages = images.slice(0, isMobile ? 3 : images.length);

    const { scrollYProgress } = useScroll({
        target: containerRef,
        offset: ["start 30%", "end 70%"]
    });

    const imageIndex = useTransform(scrollYProgress, [0, 1], [0, displayImages.length - 1]);

    useEffect(() => {
        const unsubscribe = imageIndex.onChange((latest) => {
            if (scrollYProgress.get() === 0) {
                if (activeImage !== 0) setActiveImage(0);
            } else if (scrollYProgress.get() === 1) {
                if (activeImage !== displayImages.length - 1) setActiveImage(displayImages.length - 1);
            } else {
                const roundedIndex = Math.round(latest);
                if (roundedIndex !== activeImage && roundedIndex >= 0 && roundedIndex < displayImages.length) {
                    setActiveImage(roundedIndex);
                }
            }
        });
        return unsubscribe;
    }, [imageIndex, activeImage, displayImages.length, scrollYProgress]);

    const toggleImage = (index: number) => {
        setActiveImage(activeImage === index ? null : index); // Toggle image open/close
    };

    return (
        <div
            ref={containerRef}
            className="flex flex-col w-full h-[60vh] lg:h-[80vh] items-center justify-center gap-1"
        >
            {displayImages.map((image, index) => (
                <motion.div
                    key={index}
                    className="relative overflow-hidden rounded-3xl border cursor-pointer"
                    initial={{ width: "20rem", height: "5rem" }}
                    animate={{
                        width: "100%",
                        height: activeImage === index ? "32rem" : "5rem", // Adjust height for mobile
                    }}
                    transition={{ duration: 0.6, ease: "easeInOut" }}
                    onClick={() => toggleImage(index)} // Toggle on click
                >
                    <AnimatePresence>
                        {activeImage === index && (
                            <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                transition={{ duration: 0.4 }}
                                className="absolute h-full w-full bg-gradient-to-t from-black/60 via-transparent to-transparent z-10"
                            />
                        )}
                    </AnimatePresence>
                    <img
                        src={image.src}
                        className="size-full object-cover"
                        alt={`Gallery image ${index + 1}`}
                    />
                </motion.div>
            ))}
        </div>
    );
};

export { GalleryStack };
