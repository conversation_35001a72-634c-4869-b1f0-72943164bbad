import { getStripeConfig } from "../../../config";
import Stripe from "stripe";
import {
    type StripeConnectCore,
    type CreateAccountLinkInput,
    type AccountLinkResponse,
    StripeOperationError,
} from "./core";

// =====================================================
// HELPER FUNCTIONS
// =====================================================

async function getConvexUser(userId: string) {
    // TODO: Implement get user from Convex instead of Supabase
    // const supabase = await createAdminClient();
    // const { data: user, error } = await supabase
    //     .from("user_profile")
    //     .select("email, display_name")
    //     .eq("id", userId)
    //     .single();

    // if (error || !user) {
    //     throw new NotFoundError("User", userId);
    // }
    // return user;
}

// =====================================================
// IMPLEMENTATION
// =====================================================

class StripeConnectServer implements StripeConnectCore {
    private stripe: Stripe;

    constructor() {
        const { STRIPE_SECRET_KEY } = getStripeConfig();
        if (!STRIPE_SECRET_KEY) {
            throw new Error("STRIPE_SECRET_KEY is not configured.");
        }
        this.stripe = new Stripe(STRIPE_SECRET_KEY);
    }

    private async getStripeAccountId(
        userId: string,
        profileType: 'coach' | 'facility_manager'
    ): Promise<string | null> {
        // TODO: Implement get stripe account id from Convex instead of Supabase
        return null;
        // const tableName =
        //     profileType === "coach"
        //         ? "coach_profiles"
        //         : "facility_manager_profiles";

        // const { data, error } = await supabase
        //     .from(tableName)
        //     .select("stripe_account_id")
        //     .eq("user_id", userId)
        //     .single();

        // if (error && error.code !== 'PGRST116') { // 'PGRST116' means no rows found, which is fine
        //     throw new StripeOperationError(error.message, error);
        // }

        // return data?.stripe_account_id || null;
    }

    async createAccountLink(
        input: CreateAccountLinkInput
    ): Promise<AccountLinkResponse> {
        const { userId, profileType, returnUrl, refreshUrl } = input;

        // TODO: Implement create account link with Convex instead of Supabase
        return {
            url: "",
            created: 0,
            expires_at: 0,
        };
        // try {
        //     let accountId = await this.getStripeAccountId(userId, profileType);
        //     let account;

        //     if (accountId) {
        //         account = await this.stripe.accounts.retrieve(accountId);
        //     } else {
        //         const user = await getSupabaseUser(userId);
        //         const newAccount = await this.stripe.accounts.create({
        //             type: "express",
        //             email: user.email,
        //             business_type: "individual",
        //             business_profile: { name: user.display_name || undefined },
        //         });
        //         accountId = newAccount.id;
        //         account = newAccount;

        //         // Save the new account ID to the user's profile
        //         const supabase = await createAdminClient();
        //         const tableName =
        //             profileType === "coach"
        //                 ? "coach_profiles"
        //                 : "facility_manager_profiles";
        //         await supabase
        //             .from(tableName)
        //             .update({
        //                 stripe_account_id: accountId,
        //                 stripe_account_status: "onboarding",
        //             })
        //             .eq("user_id", userId);
        //     }

        //     // If the account has details submitted, generate a login link to the Express dashboard
        //     if (account.details_submitted) {
        //         const loginLink = await this.stripe.accounts.createLoginLink(accountId);
        //         return {
        //             url: loginLink.url,
        //             created: loginLink.created,
        //             expires_at: Date.now() / 1000 + 300, // Login links are short-lived, but no expiry is returned
        //         };
        //     }

        //     // Otherwise, generate an onboarding link
        //     const accountLink = await this.stripe.accountLinks.create({
        //         account: accountId,
        //         refresh_url: refreshUrl,
        //         return_url: returnUrl,
        //         type: "account_onboarding",
        //     });

        //     return {
        //         url: accountLink.url,
        //         created: accountLink.created,
        //         expires_at: accountLink.expires_at,
        //     };
        // } catch (error: any) {
        //     if (error instanceof StripeOperationError || error instanceof NotFoundError) {
        //         throw error;
        //     }
        //     throw new StripeOperationError(error.message, error);
        // }
    }
}

// Export a singleton instance of the server-side implementation
export const stripeConnectServer: StripeConnectCore =
    new StripeConnectServer(); 