"use client";

import { motion } from "motion/react";
import { useI18n } from "@curatd/shared/locales/client";
import CuratdLogo from "@curatd/ui/components/curatd-logo";
import Link from "next/link";

export function Footer() {
  const t = useI18n();

  const sectionVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        when: "beforeChildren",
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <motion.footer
      className="bg-background border-t border-border/20 py-16 pb-28 md:pb-6"
      variants={sectionVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
    >
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8 mb-12">
          {/* Brand */}
          <motion.div className="md:col-span-2" variants={itemVariants}>
            <div className="mb-4">
              <CuratdLogo size="large" animated={false} />
            </div>
            <p className="text-muted-foreground mb-4">
              {t("landing.footer.brand.description")}
            </p>
          </motion.div>

          {/* Quick Links */}
          <motion.div variants={itemVariants}>
            <h4 className="font-bold text-foreground mb-4">
              {t("landing.footer.quickLinks.title")}
            </h4>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t("landing.footer.quickLinks.forAthletes")}
                </Link>
              </li>
              <li>  
                <Link
                  href="/coach"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t("landing.footer.quickLinks.forCoaches")}
                </Link>
              </li>
              <li>
                <Link
                  href="/gym"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t("landing.footer.quickLinks.forGyms")}
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t("landing.footer.quickLinks.contact")}
                </Link>
              </li>
            </ul>
          </motion.div>

          {/* Legal */}
          <motion.div variants={itemVariants}>
            <h4 className="font-bold text-foreground mb-4">
              {t("landing.footer.legal.title")}
            </h4>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/privacy"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t("landing.footer.legal.privacyPolicy")}
                </Link>
              </li>
              <li>
                <Link
                  href="/terms"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t("landing.footer.legal.termsOfService")}
                </Link>
              </li>
            </ul>
          </motion.div>
        </div>

        {/* Bottom Bar */}
        <motion.div
          className="pt-8 border-t border-border/20 flex flex-col md:flex-row justify-between items-center"
          variants={itemVariants}
        >
          <p className="text-muted-foreground text-sm mb-4 md:mb-0">
            © {new Date().getFullYear()} {t("landing.footer.bottomBar.copyright")}
          </p>
          <div className="flex space-x-6">
            <p className="text-muted-foreground text-sm mb-4 md:mb-0">
              {t("landing.footer.bottomBar.madeBy")}
            </p>
          </div>
        </motion.div>
      </div>
    </motion.footer>
  );
}
