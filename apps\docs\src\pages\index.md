---
title: "Curatd Documentation"
layout: "../layouts/MainLayout.astro"
---

# Curatd Documentation

Welcome to the Curatd developer documentation. This documentation provides detailed information about the various parts of the Curatd platform.

## Documentation Sections

### Backend

The backend is built with Convex, providing real-time data storage and synchronization with a focus on type safety, scalability, and developer experience. Our backend uses a domain-driven design approach with clearly defined use cases and follows strict Convex best practices.

- [Backend Overview](/backend)
- [Backend Structure](/backend/structure)
- [Database Schema](/backend/schema)
- [Authentication & Authorization](/backend/auth)
- [Creating New Use Cases](/backend/creating-use-cases)
- [Function Calling](/backend/function-calling)
- [Best Practices](/backend/best-practices)

### Frontend (Coming Soon)

- Web Application
- Mobile Application
- Admin Panel
