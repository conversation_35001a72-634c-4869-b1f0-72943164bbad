import { QueryCtx, MutationCtx } from "@/convex/_generated/server";
import { Doc } from "@/convex/_generated/dataModel";

export const loadUserRelationships = async (ctx: QueryCtx | MutationCtx, user: Doc<"users">) => {
    const queries: Promise<any>[] = [];
    if (user.customerId) {
        queries.push(ctx.db.get(user.customerId));
    }
    if (user.coachId) {
        queries.push(ctx.db.get(user.coachId));
    }
    if (user.facilityManagerId) {
        const facilityManagerId = user.facilityManagerId;
        queries.push(ctx.db.get(facilityManagerId));
        queries.push(
            ctx.db
                .query("facilities")
                .withIndex("by_manager", q => q.eq("facilityManagerId", facilityManagerId))
                .collect()
        );
    }
    const [customer, coach, facilityManager, managedFacilities] = await Promise.all(queries);
    return { customer, coach, facilityManager, managedFacilities } as {
        customer: Doc<"customers"> | null,
        coach: Doc<"coaches"> | null,
        facilityManager: Doc<"facilityManagers"> | null,
        managedFacilities: Doc<"facilities">[] | null,
    };
};