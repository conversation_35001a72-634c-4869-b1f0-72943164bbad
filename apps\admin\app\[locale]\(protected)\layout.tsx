import { redirect } from "next/navigation";
import { AppSidebar } from "@curatd/ui/components/app-sidebar";
import { SiteHeader } from "@/components/sidebar/site-header";
import { SidebarInset, SidebarProvider } from "@curatd/ui/components/sidebar";
import { fetchQuery } from "convex/nextjs";
import { api } from "@curatd/backend/api";
import { convexAuthNextjsToken } from "@convex-dev/auth/nextjs/server";
import { Enums } from "@curatd/backend";

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await fetchQuery(
    api.functions.currentUser.get,
    {},
    {
      token: await convexAuthNextjsToken(),
    }
  );
  if (!user) {
    redirect("/auth/login");
  }

  if (!user.roles.includes(Enums.Roles.ADMIN)) {
    redirect("/unauthorized");
  }

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {children}
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
