"use client";

import { Pressable, View, Text } from "react-native";
import { Check, <PERSON>, <PERSON><PERSON><PERSON>, Building2 } from "lucide-react-native";

export type UserRole = "regular_user" | "coach" | "facility_manager";

interface RoleSelectionCardProps {
  role: UserRole;
  title: string;
  description: string;
  features: string[];
  isSelected: boolean;
  onSelect: (role: UserRole) => void;
  selectText: string;
  selectedText: string;
}

const roleIcons = {
  regular_user: Users,
  coach: <PERSON><PERSON><PERSON>,
  facility_manager: Building2,
};

const roleColors = {
  regular_user: "#3B82F6",
  coach: "#10B981",
  facility_manager: "#8B5CF6",
};

export function RoleSelectionCard({
  role,
  title,
  description,
  features,
  isSelected,
  onSelect,
  selectText,
  selectedText,
}: RoleSelectionCardProps) {
  const Icon = roleIcons[role];
  const color = roleColors[role];

  return (
    <Pressable
      onPress={() => onSelect(role)}
      className={`
        relative p-6 rounded-2xl border-2 mb-4 mx-4
        ${
          isSelected
            ? "border-blue-500 bg-blue-50 shadow-lg"
            : "border-gray-200 bg-white shadow-sm"
        }
      `}
      style={{
        elevation: isSelected ? 8 : 2,
      }}
    >
      {/* Selection indicator */}
      {isSelected && (
        <View className="absolute -top-2 -right-2 bg-blue-500 rounded-full p-2 shadow-lg">
          <Check size={16} color="white" />
        </View>
      )}

      <View className="items-center mb-4">
        {/* Icon */}
        <View
          className={`
            p-4 rounded-2xl mb-4 border-2
            ${isSelected ? "bg-blue-100 border-blue-300" : "bg-gray-50 border-gray-200"}
          `}
        >
          <Icon size={32} color={isSelected ? "#3B82F6" : color} />
        </View>

        <Text
          className={`text-xl font-bold text-center mb-2 ${isSelected ? "text-blue-900" : "text-gray-900"}`}
        >
          {title}
        </Text>

        <Text
          className={`text-center text-sm ${isSelected ? "text-blue-700" : "text-gray-600"}`}
        >
          {description}
        </Text>
      </View>

      {/* Features list */}
      <View className="mb-6">
        {features.map((feature, index) => (
          <View key={index} className="flex-row items-center mb-2">
            <View
              className={`w-1.5 h-1.5 rounded-full mr-3 ${isSelected ? "bg-blue-500" : "bg-gray-400"}`}
            />
            <Text
              className={`text-sm flex-1 ${isSelected ? "text-blue-800" : "text-gray-600"}`}
            >
              {feature}
            </Text>
          </View>
        ))}
      </View>

      {/* Selection button */}
      <Pressable
        onPress={() => onSelect(role)}
        className={`
          py-3 px-6 rounded-xl border items-center justify-center flex-row
          ${
            isSelected
              ? "bg-blue-500 border-blue-500"
              : "bg-white border-gray-300"
          }
        `}
      >
        {isSelected && (
          <Check size={16} color="white" style={{ marginRight: 8 }} />
        )}
        <Text
          className={`font-semibold ${isSelected ? "text-white" : "text-gray-700"}`}
        >
          {isSelected ? selectedText : selectText}
        </Text>
      </Pressable>
    </Pressable>
  );
}
