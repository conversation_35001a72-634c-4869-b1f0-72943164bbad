import { getCurrentUser } from "@curatd/shared/backend/use-cases/user-management/server";
import { redirect } from "next/navigation";

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Check if the user has the coach role + an associated table row (coach_profiles).
  const user = await getCurrentUser();
  const hasCompletedOnboarding = user.user_roles && user.user_roles.length > 0;

  if (!hasCompletedOnboarding) {
    return redirect("/app/onboarding");
  }

  if (!user.coach_profiles) {
    return redirect("/app/business");
  }

  return <div>{children}</div>;
}
