---
title: Backend Structure
layout: ../../layouts/MainLayout.astro
---

# Backend Structure

The backend package is organized in a structured manner to support domain-driven design and separation of concerns.

## Directory Structure

```
packages/backend/
├── convex/                  # Convex backend setup
│   ├── _generated/          # Auto-generated Convex types and APIs
│   ├── functions/           # Backend functions organized by use cases
│   │   ├── useCases/        # Domain-specific use cases
│   │   │   └── admin/       # Admin-specific use cases
│   │   │       └── userManagement/ # User management use cases
│   ├── auth.config.ts       # Authentication configuration
│   ├── auth.ts              # Authentication setup
│   ├── http.ts              # HTTP endpoints configuration
│   ├── ResendOTP.ts         # OTP functionality for authentication
│   └── schema.ts            # Database schema definition
├── helpers/                 # Helper functions
│   ├── admin/               # Admin-specific helpers
│   └── auth/                # Authentication helpers
├── types/                   # TypeScript type definitions
│   ├── dtos/                # Data Transfer Objects
│   ├── enums.ts             # Enumeration types
│   ├── enumValidators.ts    # Validators for enums
│   └── tableValidators.ts   # Database table validators
├── wrappers/                # Higher-order functions 
│   └── auth/                # Authentication wrappers like withRole
└── index.ts                 # Main export file
```

## Key Components

### Convex Setup

The `convex/` directory contains the core of our backend setup:

- **schema.ts**: Defines all database tables and their relationships
- **auth.ts & auth.config.ts**: Configure authentication mechanisms
- **http.ts**: Sets up HTTP endpoints for external integrations

### Functions Organization

Backend functions in `convex/functions/` are organized by use cases following a domain-driven design approach:

```
functions/
├── currentUser.ts           # User-specific functions
└── useCases/                # Domain-specific functions 
    ├── admin/               # Admin panel functions
    │   └── userManagement/  # User management operations
    └── [other domains]/     # Other domain-specific functions
```

### Helpers and Wrappers

- **Helpers**: Utility functions for common operations
- **Wrappers**: Higher-order functions that enhance base functionality, such as role-based authorization

### Types

The `types/` directory contains all TypeScript type definitions:

- **DTOs**: Data Transfer Objects for function input/output
- **Enums**: Enumerated types used throughout the application
- **Validators**: Validation schemas for database and function inputs

## Exports

The main `index.ts` file exports:

```typescript
export * as Enums from "./types/enums";
export * as ConvexValidators from "./types/enumValidators";
export * as DTOs from "./types/dtos";
```

These exports make types available to other packages in the monorepo that interact with the backend.
