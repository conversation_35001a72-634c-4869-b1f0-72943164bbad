"use client";

import { motion } from "motion/react";
import { useI18n } from "@curatd/shared/locales/client";
import { cn } from "@curatd/ui/lib/utils";

interface LogoProps {
  className?: string;
  onClick?: () => void;
  showIcon?: boolean;
  size?: "default" | "large" | "extra-large";
  variant?: "default" | "admin" | "business" | "pro";
  animated?: boolean;
  color?: "white" | "primary";
}

export default function CuratdLogo({
  className,
  onClick,
  showIcon = false,
  size = "default",
  variant = "default",
  animated = true,
  color = "primary",
}: LogoProps) {
  const t = useI18n();

  const logoContent = (
    <div
      className={cn(
        "flex items-center gap-2",
        size === "large" ? "text-2xl" : size === "extra-large" ? "text-4xl" : "text-xl",
        className
      )}
    >
      {showIcon && (
        <div
          className={cn(
            "rounded-lg bg-primary flex items-center justify-center text-primary-foreground font-bold",
            size === "large" ? "w-10 h-10 text-lg" : size === "extra-large" ? "w-12 h-12 text-xl" : "w-8 h-8 text-sm"
          )}
        >
          C
        </div>
      )}
      <div className="flex items-baseline">
        <span
          className={cn(
            "font-bold tracking-widest",
            "dark:text-primary-foreground",
            color === "white" ? "text-primary-foreground" : "text-primary",
            size === "large" ? "text-2xl" : size === "extra-large" ? "text-4xl" : "text-xl"
          )}
        >
          {t("nav.brand")}
        </span>
        {variant === "admin" ? (
          <span className="text-sm text-muted-foreground">admin</span>
        ) : variant === "business" ? (
          <span className="text-sm text-muted-foreground">business</span>
        ) : variant === "pro" ? (
          <span className="text-sm text-muted-foreground">pro</span>
        ) : null}
      </div>
    </div>
  );

  if (onClick) {
    return (
      <motion.button
        onClick={onClick}
        className={cn(
          "transition-colors hover:opacity-80 cursor-pointer rounded-lg",
          className
        )}
        whileHover={animated ? { scale: 1.05 } : undefined}
        whileTap={animated ? { scale: 0.95 } : undefined}
      >
        {logoContent}
      </motion.button>
    );
  }

  return <div className={className}>{logoContent}</div>;
}
