---
title: Database Schema
layout: ../../layouts/MainLayout.astro
---

# Database Schema

Curatd uses Convex as its database, which provides a document-based storage system with real-time capabilities. The schema is defined in `convex/schema.ts`.

## Schema Design

The schema is designed for a high-end sport coaching marketplace with considerations for:

- Scale
- Flexibility
- Legal compliance
- Auditability
- Internationalization
- Multi-vendor e-commerce
- Advanced analytics

## Core Tables

The database consists of numerous tables that represent different entities in the system:

### User-Related Tables

- **users**: Core user information
- **invitations**: System for inviting users with role assignments
- **userMedia**: User-related media files (profile pictures, etc.)

### Business Entities

- **coaches**: Coach profiles and qualifications
- **customers**: Customer information
- **facilities**: Sport facilities
- **facilityManagers**: Management staff for facilities
- **facilityAssets**: Media assets associated with facilities

### Products and Commerce

- **products**: Main product listings
- **productVariants**: Different variations of products
- **addons**: Additional services or features
- **membershipPlans**: Subscription offerings
- **subscriptions**: User subscription records
- **orders**: Purchase orders
- **invoices**: Billing records

### Scheduling

- **calendars**: Available scheduling calendars
- **calendarEvents**: Scheduled events
- **sessionInstances**: Specific instances of coaching sessions
- **userEvents**: User-specific event records

### Communication

- **chatThreads**: Conversation containers
- **chatMessages**: Individual messages
- **chatMessageAttachments**: Files attached to messages
- **chatParticipants**: Users in conversations
- **chatMessageReactions**: Reactions to messages
- **notifications**: System notifications

### Marketing and Promotions

- **discounts**: Promotional discounts
- **discountUsages**: Records of discount applications
- **affiliationCodes**: Referral and affiliate codes
- **affiliationUses**: Records of affiliate code usage
- **buddyPasses**: Friend referral passes
- **waitingList**: Waitlist for limited offerings

## Schema Definition Pattern

Tables are defined using the following pattern:

```typescript
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  tableName: defineTable(tableNameValidator)
    .index("by_indexName", ["fieldName"])
    .index("by_anotherIndex", ["field1", "field2"]),
});
```

Each table has a corresponding validator that defines its structure and field types, imported from `../types/tableValidators`. For example:

```typescript
// In types/tableValidators.ts
export const usersTableValidator = v.object({
  name: v.string(),
  email: v.string(),
  role: v.string(),
  active: v.boolean()
});

// In convex/schema.ts
import { usersTableValidator } from "../types/tableValidators";

const users = defineTable(usersTableValidator)
  .index("by_email", ["email"])
  .index("by_role", ["role"]);
```

Note that system fields like `_id` and `_creationTime` are automatically added to all documents by Convex.

## Search Indexes

For tables that require text search capabilities, search indexes are defined using:

```typescript
const tableName = defineTable(tableNameValidator)
    .index("by_indexName", ["fieldName"])
    .searchIndex("search_fieldName", {
        searchField: "fieldName",
        filterFields: ["field1", "field2"]
    });
```

When querying with search indexes, use the following pattern:

```typescript
// Query for "10 messages in channel '#general' that best match 'hello hi'"
const messages = await ctx.db
  .query("messages")
  .withSearchIndex("search_body", (q) =>
    q.search("body", "hello hi").eq("channel", "#general")
  )
  .take(10);
```

## Index Design

Indexes are carefully designed to support query patterns and are named with a consistent convention:

- `by_fieldName` for single field indexes
- `by_field1_and_field2` for compound indexes
- `search_fieldName` for search indexes

## Table Relationships

Relationships between tables are managed through ID references. For example:
- A `sessionInstance` references a `calendar` through `calendarId`
- `chatParticipants` references both `chatThreads` and `users`

This design enables efficient querying while maintaining the flexibility of a document database.
