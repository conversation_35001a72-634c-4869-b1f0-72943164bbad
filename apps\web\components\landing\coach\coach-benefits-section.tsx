"use client";

import { Badge } from "@curatd/ui/components/badge";
import { Title } from "../title";
import { useI18n } from "@curatd/shared/locales/client";
import { VideoCarousel } from "../video-carousel";

export function CoachBenefitsSection() {
    const t = useI18n();

    const benefits = [
        {
            title: t('landing.coach.benefits.items.flexibility.title'),
            subtitle: t('landing.coach.benefits.items.flexibility.subtitle'),
            description: t('landing.coach.benefits.items.flexibility.description'),
            badge: t('landing.coach.benefits.items.flexibility.badge'),
        },
        {
            title: t('landing.coach.benefits.items.travelWork.title'),
            subtitle: t('landing.coach.benefits.items.travelWork.subtitle'),
            description: t('landing.coach.benefits.items.travelWork.description'),
            badge: t('landing.coach.benefits.items.travelWork.badge'),
        },
        {
            title: t('landing.coach.benefits.items.expertise.title'),
            subtitle: t('landing.coach.benefits.items.expertise.subtitle'),
            description: t('landing.coach.benefits.items.expertise.description'),
            badge: t('landing.coach.benefits.items.expertise.badge'),
        },
        {
            title: t('landing.coach.benefits.items.growth.title'),
            subtitle: t('landing.coach.benefits.items.growth.subtitle'),
            description: t('landing.coach.benefits.items.growth.description'),
            badge: t('landing.coach.benefits.items.growth.badge'),
        }
    ];

    const videoUrl = "https://videos.pexels.com/video-files/12188782/12188782-uhd_2560_1440_25fps.mp4"

    return (
        <section id="coach" className="bg-background py-8 sm:py-12 md:py-16 lg:py-20">
            <div className="md:text-center">
                <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <Title
                        title={t('landing.coach.benefits.title')}
                        showSeparator={true}
                        animated={true}
                    />
                </div>
            </div>
            <div className="container mx-auto mt-12 px-4 sm:px-6 lg:px-8">
                <VideoCarousel
                    data={benefits}
                    videoUrl={videoUrl}
                    renderContent={(item) => (
                        <>
                            <Badge variant="secondary" className="mb-3">
                                {item.badge}
                            </Badge>
                            <h3 className="text-2xl md:text-3xl font-bold mb-3">{item.title}</h3>
                            <p className="text-base md:text-lg mb-2">{item.subtitle}</p>
                            <p className="text-base md:text-lg opacity-80">{item.description}</p>
                        </>
                    )}
                />
            </div>
        </section>
    );
}
