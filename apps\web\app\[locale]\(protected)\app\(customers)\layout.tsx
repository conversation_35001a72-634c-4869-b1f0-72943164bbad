import { redirect } from "next/navigation";
import { getCurrentUser } from "@curatd/shared/backend/use-cases/user-management/server";

export default async function CustomerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // This layout protects the customer-facing application.
  // It ensures the user has the 'customer' role.
  const user = await getCurrentUser();

  const roles = user.user_roles.map((ur) => ur.roles.code);

  const hasCompletedOnboarding = roles && roles.length > 0;

  if (!hasCompletedOnboarding) {
    return redirect("/app/onboarding");
  }

  // If the user does not have the 'customer' role, they might be a business user.
  if (!roles.includes("customer")) {
    // Check for business roles and redirect accordingly.
    if (roles.includes("coach")) {
      return redirect("/app/business/coach");
    }
    if (roles.includes("facility_manager")) {
      return redirect("/app/business/facility-manager");
    }
    // If they have some other role but not a recognized one, redirect to a safe page.
    // This case should ideally not happen in a normal flow.
    return redirect("/auth/login?error=InvalidRole");
  }

  // User is a customer, render the customer application.
  return <>{children}</>;
}
