{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@convex-dev/auth": "^0.0.87", "@convex-dev/eslint-plugin": "0.0.1-alpha.4", "@curatd/backend": "workspace:*", "@curatd/shared": "workspace:*", "@curatd/ui": "workspace:*", "@hookform/resolvers": "^3.10.0", "@tabler/icons-react": "^3.34.0", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "convex": "^1.25.4", "lucide-react": "^0.522.0", "next": "15.3.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "recharts": "^2.15.4", "sonner": "^2.0.5", "stripe": "^17.3.1", "swr": "^2.2.6", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@curatd/eslint-config": "workspace:*", "@curatd/typescript-config": "workspace:*", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "eslint": "^9", "eslint-config-next": "15.3.4", "typescript": "^5.7.3"}}