---
title: Best Practices
layout: ../../layouts/MainLayout.astro
---

# Backend Best Practices

This document outlines best practices for working with the Curatd backend, following Convex's guidelines and our project standards.

## Convex Functions

### Function Syntax

Always use the new function syntax for Convex functions, with proper argument and return value validators:

```typescript
import { query } from "./_generated/server";
import { v } from "convex/values";

export const myFunction = query({
  args: {
    param1: v.string(),
    param2: v.optional(v.number())
  },
  returns: v.object({
    success: v.boolean(),
    message: v.string()
  }),
  handler: async (ctx, args) => {
    // Implementation...
    return {
      success: true,
      message: "Operation successful"
    };
  },
});
```

### Function Naming

- Use clear, descriptive names that indicate the action being performed
- Follow consistent naming patterns for similar operations:
  - `list`: For retrieving multiple records
  - `get`: For retrieving a single record
  - `create`: For creating new records
  - `update`: For updating records
  - `delete`: For removing records
  - `toggle`: For boolean state changes

### Function Types and Registration

- Use appropriate function types based on operation:
  - `query`: For read-only operations (retrieving data)
  - `mutation`: For write operations (creating, updating, or deleting data)
  - `action`: For operations that interact with external systems or need Node.js runtime

- For private functions that should only be callable from other Convex functions:
  - `internalQuery`: For internal read-only operations 
  - `internalMutation`: For internal write operations
  - `internalAction`: For internal operations that interact with external systems

- Always import function registration functions from `./_generated/server`:

```typescript
import { query, mutation, action, internalQuery, internalMutation, internalAction } from "./_generated/server";
```

- When adding Node.js functionality in actions, add the Node.js directive:

```typescript
"use node";
import { action } from "./_generated/server";
import * as fs from "fs/promises";
```

### Function Validation

- Always use validators for function arguments and return values
- For nullable values, use `v.optional(v.null())` or explicitly return `v.null()`
- Use appropriate validators for ID fields: `v.id("tableName")`

```typescript
import { query } from "./_generated/server";
import { v } from "convex/values";

// Complete validator example
export const exampleFunction = query({
  args: {
    id: v.id("users"),
    name: v.optional(v.string()),
    filters: v.optional(v.object({
      status: v.string(),
      role: v.string()
    })),
    simpleArray: v.array(v.union(v.string(), v.number())),
  },
  returns: v.array(v.object({
    _id: v.id("users"),
    name: v.string(),
    email: v.string()
  })),
  handler: async (ctx, args) => {
    // Implementation...
  }
});

// Function with null return
export const voidFunction = mutation({
  args: { id: v.id("users") },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Do something without returning a value
    await ctx.db.patch(args.id, { lastUpdated: Date.now() });
    return null;
  }
});

// Using discriminated union types
export const functionWithUnion = query({
  args: {
    filter: v.union(
      v.object({
        kind: v.literal("date"),
        date: v.string()
      }),
      v.object({
        kind: v.literal("status"),
        status: v.string()
      })
    )
  },
  returns: v.string(),
  handler: async (ctx, args) => {
    if (args.filter.kind === "date") {
      return `Filtering by date: ${args.filter.date}`;
    } else {
      return `Filtering by status: ${args.filter.status}`;
    }
  }
});
```

## Database Operations

### Querying

- Use appropriate indexes for queries to avoid table scans
- Do NOT use `filter` in queries; always define an index in the schema and use `withIndex` instead
- Follow the index order when filtering by multiple fields
- Use pagination for large result sets
- Avoid unnecessary `.collect()` calls for large datasets
- Use `.unique()` to get a single document that matches a query (throws if multiple matches)

```typescript
// Good: Using an index
const users = await ctx.db
  .query("users")
  .withIndex("by_role", (q) => q.eq("role", "admin"))
  .collect();

// Better: Adding pagination
const users = await ctx.db
  .query("users")
  .withIndex("by_role", (q) => q.eq("role", "admin"))
  .order("desc")
  .paginate(args.paginationOpts);

// Getting a unique document
const user = await ctx.db
  .query("users")
  .withIndex("by_email", (q) => q.eq("email", args.email))
  .unique();

// Using search index
const searchResults = await ctx.db
  .query("products")
  .withSearchIndex("search_description", (q) => 
    q.search("description", "premium workout")
  )
  .take(10);
```

For pagination, define the validator:

```typescript
import { paginationOptsValidator } from "convex/server";

export const listWithPagination = query({
  args: { 
    paginationOpts: paginationOptsValidator,
    category: v.string() 
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("products")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .order("desc")
      .paginate(args.paginationOpts);
  },
});
```

### Mutations

- Use transactions (multiple operations in a single mutation) to ensure data consistency
- Prefer `patch` for partial updates instead of `replace` for full document overwrites
- Always validate data before inserting or updating

```typescript
// Partial update with patch
await ctx.db.patch(userId, { 
  name: args.name,
  updatedAt: Date.now()
});

// Transaction example
await ctx.db.patch(userId, { 
  points: args.points
});
await ctx.db.insert("pointHistory", {
  userId,
  points: args.points,
  reason: args.reason
});
```

## Error Handling

- Use consistent error messages and throw patterns
- Include descriptive error messages that help with debugging
- Create specific error types for different scenarios

```typescript
// Not found error
if (!user) {
  throw new Error("User not found");
}

// Permission error
if (!userHasRole(ctx, Roles.ADMIN)) {
  throw new Error("Insufficient permissions");
}

// Validation error
if (args.amount <= 0) {
  throw new Error("Amount must be greater than zero");
}
```

## Type Safety

- Leverage TypeScript's type system for maximum safety
- Use proper return type annotations to ensure function outputs match expected types
- Use the generated `Doc<"tableName">` types for database documents

```typescript
import { Doc } from "./_generated/dataModel";

// Type-safe function with explicit return type
async function getUser(ctx: QueryCtx, id: Id<"users">): Promise<Doc<"users"> | null> {
  return await ctx.db.get(id);
}
```

## Security

- Always validate user permissions using `withRole` or similar wrappers
- Don't trust client inputs; validate all data before use
- Be cautious with search indexes and filters to prevent data leakage
- Use field-level security when necessary by filtering sensitive fields

```typescript
// Secure function with role check
export const sensitiveOperation = mutation({
  args: { /* ... */ },
  returns: v.null(),
  handler: withRole(Roles.ADMIN, async (ctx, args) => {
    // Only admins can perform this operation
  })
});
```

## Performance

- Use appropriate indexes for queries
- Avoid unnecessary database reads or writes
- Use pagination for large result sets
- Leverage Convex search indexes for text search operations

```typescript
// Efficient text search
const results = await ctx.db
  .query("products")
  .withSearchIndex("search_name", (q) => 
    q.search("name", args.search)
  )
  .paginate(args.paginationOpts);
```

## Testing

- Test all functions with different input scenarios
- Verify role-based access control works as expected
- Test error cases to ensure proper error handling
- Use console.log for debugging during development

## Documentation

- Add inline comments for complex logic
- Document function purposes and parameters
- Keep README and documentation files updated
- Document database schema changes
