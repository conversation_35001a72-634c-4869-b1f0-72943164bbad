import { v } from "convex/values";
import {
    roleValidator,
    invitationStatusValidator,
    documentVerificationTypeValidator,
    facilityTypeValidator,
    subscriptionStatusValidator,
    productVariantTypeValidator,
    addonsOwnerTypeValidator,
    orderStatusValidator,
    paymentStatusValidator,
    discountMethodValidator,
    notificationStatusValidator,
    waitingListIntentValidator,
    adminRoleValidator,
    coachRoleValidator,
    facilityManagerRoleValidator,
    stripeConnectValidator
} from "../types/enumValidators";


/*──────────────────────── INVITATIONS ─────────────────────────*/
/**
 * Invitation flow:
 *  1. Manager/Admin generates an invite → record created with unique token.
 *  2. Invitee clicks link / signs up → backend validates token + email.
 *  3. On success: `accepted` set true and `acceptedUserId` populated.
 */
export const invitationsTableValidator = {
    email: v.string(),                    // Invitee email (unique per invite)
    token: v.string(),                    // Cryptographically random, URL-safe
    invitedBy: v.id("users"),             // Who sent the invitation
    roleSuggested: v.optional(            // Optional suggested role(s)
        v.array(
            roleValidator
        )
    ),
    accepted: v.boolean(),                // Has the invite been redeemed?
    acceptedUserId: v.optional(v.id("users")),
    createdAt: v.string(),                // ISO-8601
    acceptedAt: v.optional(v.string())    // ISO-8601
};

/*──────────────────────── USERS & ROLES ────────────────────────*/
/**
 * Users are authentication entities—used to log in, receive notifications, 
 * own data, and (via `roles`) link to one or more business personas.
 * Every user can have multiple concurrent roles (coach, facilityManager, etc).
 *
 * All role-specific data is found in the corresponding entity tables
 * (`coaches`, `facilityManagers`, `customers`), linked via IDs.
 */

export const usersTableValidator = {
    /** Whether the user is active */
    isActive: v.boolean(),
    /** Unique email for login, communication, password recovery */
    email: v.string(),
    /** Auth provider type (e.g., 'google' or 'email') */
    provider: v.union(v.literal("email"), v.literal("google")),
    /** Provider-specific unique user id (if applicable) */
    providerId: v.optional(v.string()),
    /** Password hash (if email auth) */
    hashedPassword: v.optional(v.string()),
    /** Fallback avatar from oauth provider */
    fallbackAvatarUrl: v.optional(v.string()),
    /** Email verification time (ISO-8601) */
    emailVerificationTime: v.optional(v.number()),
    /** Phone number (if applicable) */
    phone: v.optional(v.string()),
    /** Phone verification time (ISO-8601) */
    phoneVerificationTime: v.optional(v.number()),
    /** Whether the user is anonymous */
    isAnonymous: v.optional(v.boolean()),
    /** Whether the user is verified */
    isVerified: v.optional(v.boolean()),
    /** Name of the user */
    name: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),

    /** Multi-role support: every user can be multiple personas */
    roles: v.array(
        roleValidator
    ),

    /** One-to-one links to personas, if the user "owns" them */
    customerId: v.optional(v.id("customers")),
    coachId: v.optional(v.id("coaches")),
    facilityManagerId: v.optional(v.id("facilityManagers")),

    /** User creation datetime (ISO-8601 with TZ offset) */
    createdAt: v.string()
};

/*──────────────────────── CUSTOMERS ───────────────────────────*/
/**
 * "Customers" are people who book/pay for sport sessions and manage
 * their profile, invoices, calendar, and communication on CURATD.
 *
 * Not all users are customers, but all customers are users.
 */

export const customersTableValidator = {
    userId: v.id("users"),

    /** Profile and onboarding data (sports, motivation, etc) */
    profile: v.object({
        firstName: v.string(),
        lastName: v.string(),
        gender: v.optional(v.string()),
        /** Avatar from media gallery */
        avatarMediaId: v.optional(v.id("userMedia"))
    }),
    onboarding: v.object({
        preferredSports: v.array(v.string()),
        preferredTimes: v.array(v.string()),
        preferredLocations: v.array(v.string()),
        motivation: v.optional(v.string())
    }),

    /** Miscellaneous extra data (future proof, JSON blob) */
    misc: v.optional(v.any()),

    /** Stripe customer ID for payment records (optional, used in payment system) */
    stripeCustomerId: v.optional(v.string()),

    /** Links to the customer's personal booking calendar */
    calendarId: v.id("calendars"),

    /** Entity creation datetime (ISO-8601) */
    createdAt: v.string()
}

/*──────────────────────── COACHES ─────────────────────────────*/
/**
 * "Coaches" are professional vendors on the platform: they publish courses,
 * manage their profile and certifications, receive bookings, and are paid via Stripe.
 */

export const coachesTableValidator = {
    userId: v.id("users"),

    /** Legal info for business registration & invoicing */
    legal: v.object({
        companyName: v.optional(v.string()),
        vat: v.optional(v.string()),
        address: v.optional(v.string())
    }),
    /** Stripe Connect account for payouts */
    ...stripeConnectValidator,

    /** Public-facing coach info */
    profile: v.object({
        bio: v.string(),
        sports: v.array(v.string()),
        certifications: v.optional(v.array(v.string())),
        avatarMediaId: v.optional(v.id("userMedia"))
    }),

    /** If true, coach is on vacation (no new bookings) */
    vacationMode: v.boolean(),

    /** Is the coach account active and shown to users? */
    isActive: v.boolean(),

    /** Calendar for coach's own schedule (availabilities, overrides, price) */
    calendarId: v.id("calendars"),

    /** Average rating (computed from reviews) */
    ratingAvg: v.optional(v.number()),

    createdAt: v.string()
};

/*──────────────────────── FACILITY MANAGERS ───────────────────*/
/**
 * Facility managers are users who administrate one or more facilities on behalf
 * of a business or organization. Managers may be agencies, firms, or individuals.
 *
 * Legal/business data is at the facility level.
 * Each manager is subject to strong KYC (identity) requirements.
 */

export const facilityManagersTableValidator = {
    userId: v.id("users"),

    /** Public contact data for admin panel, support, and assignment */
    profile: v.object({
        contactName: v.string(),
        phone: v.optional(v.string()),
        avatarMediaId: v.optional(v.id("userMedia"))
    }),

    /** Extensive KYC for compliance/auditability—verified by admin */
    kyc: v.optional(v.object({
        /** Identity */
        firstName: v.string(),
        lastName: v.string(),
        dateOfBirth: v.string(),          // ISO "YYYY-MM-DD"
        nationality: v.string(),          // ISO 3166-1 alpha-2 code

        /** Residential address (for anti-fraud, AML, compliance) */
        address: v.object({
            line1: v.string(),
            line2: v.optional(v.string()),
            city: v.string(),
            region: v.optional(v.string()),
            postalCode: v.string(),
            country: v.string()
        }),

        /** Document verification (scan/photo is stored in media gallery) */
        idDocument: v.object({
            type: documentVerificationTypeValidator,
            number: v.string(),
            issuingCountry: v.string(),         // ISO 3166-1 alpha-2
            expiryDate: v.optional(v.string()), // ISO "YYYY-MM-DD"
            documentMediaId: v.id("userMedia")
        }),

        /** Verification state (admin workflow) */
        verificationStatus: invitationStatusValidator,
        submittedAt: v.optional(v.string()),
        verifiedAt: v.optional(v.string())
    })),

    /** Calendar for general manager availability (if relevant) */
    calendarId: v.id("calendars"),
    createdAt: v.string()
};

/*──────────────────────── FACILITIES & ASSETS ─────────────────*/
/**
 * Each "facility" is a venue for sport sessions.
 * Legal and Stripe Connect business data is attached here (not at manager level).
 * This enables management agencies and white-label operators.
 */

export const facilitiesTableValidator = {
    /** The manager assigned to this facility */
    facilityManagerId: v.id("facilityManagers"),

    /** Facility type: for search, analytics, and display */
    type: facilityTypeValidator,

    /** Public profile and marketing info */
    name: v.string(),
    description: v.string(),
    address: v.string(),
    geo: v.optional(v.object({ lat: v.number(), lng: v.number() })),
    imageMediaIds: v.optional(v.array(v.id("userMedia"))),

    /** Capacity and usage pricing (default for bookings) */
    capacity: v.number(),
    usagePrice: v.number(),

    /** Payout and invoicing business info (used by Stripe, gov, etc.) */
    legal: v.object({
        companyName: v.string(),
        vat: v.optional(v.string()),
        address: v.string()
    }),
    /** Stripe Connect payout account for this business */
    ...stripeConnectValidator,

    payoutPercentage: v.number(),

    /** Add-on whitelist (what extras can be sold for this facility) */
    authorizedAddonIds: v.optional(v.array(v.id("addons"))),

    /** Facility state and operational flags */
    vacationMode: v.boolean(),
    calendarId: v.id("calendars"),
    isActive: v.boolean(),
    isFeatured: v.boolean(),
    createdAt: v.string()
};

/**
 * Each "facility asset" is a sub-resource (court, room, etc.)
 * All pricing, images, capacity, and add-on whitelisting can be
 * customized per asset.
 */
export const facilityAssetsTableValidator = {
    facilityId: v.id("facilities"),
    assetType: v.string(),   // e.g., "court", "room"
    name: v.string(),
    capacity: v.number(),
    usagePrice: v.number(),
    imageMediaIds: v.optional(v.array(v.id("userMedia"))),
    calendarId: v.id("calendars"),
    authorizedAddonIds: v.optional(v.array(v.id("addons"))),
    isActive: v.boolean()
};

/*──────────────────────── MEMBERSHIP & SUBSCRIPTIONS ──────────*/
/**
 * Membership plans (monthly/annual) for loyalty and access control.
 * Linked to Stripe products.
 */
export const membershipPlansTableValidator = {
    key: v.string(),    // e.g., "standard", "premium"
    title: v.string(),
    description: v.string(),
    monthlyPrice: v.number(),
    benefits: v.object({
        discountedRate: v.number(),
        buddyPasses: v.optional(v.number())
    }),
    stripeSubId: v.string()
};

/**
 * Subscriptions purchased by customers.
 * Tied to plan.
 */
export const subscriptionsTableValidator = {
    customerId: v.id("customers"),
    planId: v.id("membershipPlans"),
    status: subscriptionStatusValidator,
    startedAt: v.string(),
    expiresAt: v.optional(v.string())
};

/**
 * Track monthly or event-based usage of "buddy passes" (bring-a-friend)
 */
export const buddyPassesTableValidator = {
    subscriptionId: v.id("subscriptions"),
    usedAt: v.string(),
    orderId: v.id("orders"),
    guestEmail: v.optional(v.string()),
    invitedCustomerId: v.optional(v.id("customers"))
};

/*──────────────────────── PRODUCTS & VARIANTS ─────────────────*/
/**
 * Products: high-level "course" or "activity" (e.g., Tennis Lesson)
 * Each has a list of authorized locations (facilities or POIs)
 * and authorized add-ons.
 */
export const productsTableValidator = {
    coachId: v.id("coaches"),
    title: v.string(),
    description: v.string(),
    sportType: v.string(),
    basePrice: v.number(),
    imageMediaIds: v.optional(v.array(v.id("userMedia"))),
    isActive: v.boolean(),
    tags: v.optional(v.array(v.string())),
    authorizedFacilityIds: v.optional(v.array(v.id("facilities"))),
    authorizedPoiIds: v.optional(v.array(v.id("pointsOfInterests"))),
    authorizedAddonIds: v.optional(v.array(v.id("addons")))
};

/**
 * Variants: session types, durations, group/private, etc.
 * Variant-level location and add-on control.
 */
export const productVariantsTableValidator = {
    productId: v.id("products"),
    title: v.string(),
    description: v.optional(v.string()),
    durationMinutes: v.number(),
    type: productVariantTypeValidator,
    price: v.number(),
    maxParticipants: v.number(),
    imageMediaIds: v.optional(v.array(v.id("userMedia"))),
    authorizedFacilityIds: v.optional(v.array(v.id("facilities"))),
    authorizedAddonIds: v.optional(v.array(v.id("addons"))),
    authorizedPoiIds: v.optional(v.array(v.id("pointsOfInterests")))
};

/*─────────────── ADD-ONS (generic owner) ─────────────────────*/
/**
 * Add-ons are bookable extras or required items (equipment, towels, etc.)
 * Can be owned by either a product variant or a facility asset.
 */
export const addonsTableValidator = {
    ownerType: addonsOwnerTypeValidator,
    ownerId: v.union(
        v.id("productVariants"),
        v.id("facilityAssets")
    ),
    name: v.string(),
    price: v.number(),
    isRequired: v.boolean(),
    quantityAvailable: v.number(),
    imageMediaId: v.optional(v.id("userMedia"))
};

/*──────────────────────── POINTS OF INTERESTS ─────────────────*/
/**
 * Public points of interest—parks, outdoor spaces, etc.—created by
 * coaches or admins, and usable as session locations.
 */
export const pointsOfInterestsTableValidator = {
    creatorType: v.union(adminRoleValidator, coachRoleValidator),
    creatorId: v.union(v.id("users"), v.id("coaches")),
    name: v.string(),
    description: v.string(),
    geo: v.object({ lat: v.number(), lng: v.number() }),
    imageMediaIds: v.optional(v.array(v.id("userMedia"))),
    isActive: v.boolean(),
    createdAt: v.string()
};

/*──────────────────────── SCHEDULING ──────────────────────────*/
/**
 * Shared calendar structure (generic, for any bookable entity).
 * Supports time zone, standard weekly availabilities, per-date
 * overrides, and price overrides (for dynamic pricing).
 */
export const calendarsTableValidator = {
    ownerType: v.union(
        v.literal("coach"),
        v.literal("facilityManager"),
        v.literal("facility"),
        v.literal("asset"),
        v.literal("customer")
    ),
    ownerId: v.string(),
    timezone: v.string(),

    /** Standard weekly recurring slots (Mon-Sun) */
    availabilities: v.optional(
        v.array(
            v.object({
                day: v.string(),  // "monday", etc.
                start: v.string(), // "08:00"
                end: v.string()
            })
        )
    ),

    /** Single-date overrides for holidays, closures, or extra hours */
    availabilityOverrides: v.optional(
        v.array(
            v.object({
                date: v.string(),
                isAvailable: v.boolean(),
                start: v.optional(v.string()),
                end: v.optional(v.string())
            })
        )
    ),

    /** Calendar-based price overrides (dynamic pricing) */
    priceOverrides: v.optional(
        v.array(
            v.object({
                date: v.string(),
                start: v.string(),
                end: v.string(),
                price: v.number()
            })
        )
    )
};

/**
 * Individual events on a calendar (for bookings, blocks, etc.)
 */
export const calendarEventsTableValidator = {
    calendarId: v.id("calendars"),
    title: v.string(),
    start: v.string(),
    end: v.string(),
    metadata: v.optional(v.any())
};

/**
 * Generated session instances (for group bookings, attendance, etc.)
 * Multi-asset booking support (array of assetIds).
 */
export const sessionInstancesTableValidator = {
    productVariantId: v.id("productVariants"),
    startTime: v.string(),
    endTime: v.string(),
    facilityId: v.optional(v.id("facilities")),
    assetIds: v.optional(v.array(v.id("facilityAssets"))),
    poiId: v.optional(v.id("pointsOfInterests")),
    capacity: v.number(),
    bookings: v.array(v.id("orders")),
    waitlist: v.array(v.id("customers"))
};

/*──────────────────────── COMMERCE FLOW ───────────────────────*/
/**
 * Shopping cart (temporary, per-customer). Each cart item can hold
 * multiple assets (multi-court, multi-room), add-ons, and a location.
 */
export const cartsTableValidator = {
    customerId: v.id("customers"),
    items: v.array(
        v.object({
            productVariantId: v.id("productVariants"),
            quantity: v.number(),
            scheduledAt: v.string(),
            facilityId: v.optional(v.id("facilities")),
            assetIds: v.optional(v.array(v.id("facilityAssets"))),
            poiId: v.optional(v.id("pointsOfInterests")),
            addonIds: v.optional(v.array(v.id("addons")))
        })
    ),
    createdAt: v.string()
};

/**
 * Completed orders; each order item can have multiple assets.
 */
export const ordersTableValidator = {
    customerId: v.id("customers"),
    status: orderStatusValidator,
    totalAmount: v.number(),
    createdAt: v.string(),
    paymentIntentId: v.optional(v.string()),
    paymentStatus: paymentStatusValidator,
    items: v.array(
        v.object({
            coachId: v.id("coaches"),
            productVariantId: v.id("productVariants"),
            scheduledAt: v.string(),
            facilityId: v.optional(v.id("facilities")),
            assetIds: v.optional(v.array(v.id("facilityAssets"))),
            poiId: v.optional(v.id("pointsOfInterests")),
            price: v.number(),
            addonIds: v.optional(v.array(v.id("addons")))
        })
    ),
    invoiceUrl: v.optional(v.string())
};

/**
 * Generated PDF invoices for orders (linked to Stripe or internal system).
 */
export const invoicesTableValidator = {
    customerId: v.id("customers"),
    orderId: v.id("orders"),
    pdfUrl: v.optional(v.string()),
    amount: v.number(),
    createdAt: v.string()
};

/*───────────────────── AFFILIATIONS & PAYOUTS ─────────────────*/
/**
 * Affiliate codes (for referral programs, influencer marketing, etc.)
 */
export const affiliationCodesTableValidator = {
    coachId: v.id("coaches"),
    code: v.string(),
    attributionWindowDays: v.number(),
    createdAt: v.string()
};

/**
 * Usage log for affiliate codes (sign-ups, subscription rewards, etc.)
 */
export const affiliationUsesTableValidator = {
    codeId: v.id("affiliationCodes"),
    referredUserId: v.id("users"),
    createdAt: v.string(),
    subscriptionId: v.optional(v.id("subscriptions")),
    subscriptionStartedAt: v.optional(v.string()),
    payoutProcessed: v.boolean()
};

/*──────────────────────── DISCOUNTS ───────────────────────────*/
/**
 * Discount and promo rule engine (Shopify-level: percentage, fixed, buy-X-get-Y)
 * All scoping is by array, so rules can apply to specific products, assets, etc.
 */
export const discountsTableValidator = {
    sellerType: v.optional(
        v.union(coachRoleValidator, facilityManagerRoleValidator, adminRoleValidator)
    ),
    sellerId: v.optional(
        v.union(v.id("coaches"), v.id("facilityManagers"))
    ),
    key: v.string(),
    title: v.string(),
    description: v.string(),
    method: discountMethodValidator,
    value: v.number(),
    minQuantity: v.optional(v.number()),
    startsAt: v.string(),
    endsAt: v.string(),
    scope: v.object({
        productIds: v.optional(v.array(v.id("products"))),
        variantIds: v.optional(v.array(v.id("productVariants"))),
        facilityIds: v.optional(v.array(v.id("facilities"))),
        assetIds: v.optional(v.array(v.id("facilityAssets"))),
        addonIds: v.optional(v.array(v.id("addons")))
    }),
    buyItemIds: v.optional(
        v.object({
            variantIds: v.optional(v.array(v.id("productVariants"))),
            addonIds: v.optional(v.array(v.id("addons")))
        })
    ),
    getItemIds: v.optional(
        v.object({
            variantIds: v.optional(v.array(v.id("productVariants"))),
            addonIds: v.optional(v.array(v.id("addons")))
        })
    ),
    maxRedemptions: v.optional(v.number()),
    redemptions: v.number()
};

/**
 * Discount usage logs (per order).
 */
export const discountUsagesTableValidator = {
    discountKey: v.string(),
    orderId: v.id("orders"),
    customerId: v.id("customers"),
    usedAt: v.string()
};

/*──────────────────────── CHAT ────────────────────────────────*/
/**
 * Threaded chat (between customer/coach/facility/ops).
 * Threads can be linked to orders, support, or system contexts.
 */
export const chatThreadsTableValidator = {
    context: v.string(),     // e.g. "order:<id>"
    createdAt: v.string()
};

/**
 * Users participating in a thread.
 */
export const chatParticipantsTableValidator = {
    threadId: v.id("chatThreads"),
    userId: v.id("users"),
    joinedAt: v.string()
};

/**
 * Chat messages (plain or markdown).
 */
export const chatMessagesTableValidator = {
    threadId: v.id("chatThreads"),
    senderId: v.id("users"),
    body: v.string(),
    sentAt: v.string()
};

/**
 * Emoji reactions to messages.
 */
export const chatMessageReactionsTableValidator = {
    messageId: v.id("chatMessages"),
    reactorId: v.id("users"),
    emoji: v.string(),
    reactedAt: v.string()
};

/**
 * File attachments in chat (linked to userMedia).
 */
export const chatMessageAttachmentsTableValidator = {
    messageId: v.id("chatMessages"),
    fileId: v.string(),
    filename: v.string(),
    mimeType: v.string(),
    size: v.number(),
    uploadedAt: v.string()
};

/*──────────────────────── USER MEDIA GALLERY ──────────────────*/
/**
 * Centralized media store for all user-uploaded files (images, docs, etc).
 * Used for avatars, facility images, KYC docs, chat attachments, etc.
 */
export const userMediaTableValidator = {
    userId: v.id("users"),
    fileId: v.string(),
    filename: v.string(),
    mimeType: v.string(),
    size: v.number(),
    uploadedAt: v.string(),
    /** e.g. "coach_profile:<id>" for contextual display */
    resourceHint: v.optional(v.string())
};

/*──────────────────────── REVIEWS ─────────────────────────────*/
/**
 * Ratings & feedback from customers for coaches, post-session.
 * Used to compute coach profile scores, show testimonials, etc.
 */
export const reviewsTableValidator = {
    customerId: v.id("customers"),
    coachId: v.id("coaches"),
    orderId: v.id("orders"),
    rating: v.number(),   // 1-5 stars
    comment: v.optional(v.string()),
    createdAt: v.string()
};

/*──────────────────────── NOTIFICATIONS (audit log) ───────────*/
/**
 * Every notification (email, push, SMS, in-app, etc.) sent via Novu is 
 * persisted here for traceability, GDPR/marketing opt-out, and audit.
 * Used for unread badges, history, compliance, and operational dashboards.
 */
export const notificationsTableValidator = {
    userId: v.id("users"),
    category: v.string(),   // "order", "chat", "system", "marketing"
    channel: v.string(),    // "email", "push", "sms", "inApp", ...
    title: v.string(),
    content: v.string(),
    data: v.optional(v.any()),
    novuTxId: v.optional(v.string()),
    status: notificationStatusValidator,
    scheduledAt: v.optional(v.string()),
    deliveredAt: v.optional(v.string()),
    readAt: v.optional(v.string()),
    createdAt: v.string()
};

/*──────────────────────── USER ACTION EVENTS ──────────────────*/
/**
 * Detailed, high-volume audit and analytics log of all user actions
 * (page views, searches, product clicks, bookings, cancellations, etc).
 * Allows for advanced reporting and seller dashboards.
 *
 * Each event is atomic and tracks user, action, context, optional seller,
 * device/session, and custom metadata.
 */
export const userEventsTableValidator = {
    userId: v.id("users"),
    action: v.string(),           // e.g., "product_view", "order_create"
    targetType: v.string(),       // "product", "facility", etc.
    targetRef: v.optional(v.string()), // e.g., "products:abc123"
    sellerType: v.optional(v.union(coachRoleValidator, facilityManagerRoleValidator)),
    sellerId: v.optional(
        v.union(v.id("coaches"), v.id("facilityManagers"))
    ),
    metadata: v.optional(v.any()),    // Custom event payload (e.g., price, quantity)
    ip: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    sessionId: v.optional(v.string()),
    createdAt: v.string()
};

/*──────────────────────── WAITING LIST ────────────────────────*/
/**
 * Captures early-access requests, launch waitlists, or feature
 * interest sign-ups from people who have **not** created an account.
 *
 * Typical flow:
 *  1. Visitor submits email (and optional metadata) → row inserted.
 *  2. Ops or automated job later promotes entry to an invitation
 *     (copy email into `invitations`, send email, etc.).
 *  3. Analytics: track source, funnel, conversion rate to invitation.
 */
export const waitingListTableValidator = {
    /** Primary contact email (unique) */
    email: v.string(),

    /** Optional data to segment leads or pre-assign roles */
    intent: v.optional(waitingListIntentValidator),

    /** UTM or custom marketing source tags */
    source: v.optional(v.string()),     // e.g. "landing_page", "fb_ads"

    /** Free-text notes (survey answers, extra info) */
    notes: v.optional(v.string()),

    /** IP / location or other raw tracking info */
    metadata: v.optional(v.any()),

    /** Has this entry been promoted to an invitation? */
    invitationId: v.optional(v.id("invitations")),

    /** ISO-8601 timestamp of submission */
    createdAt: v.string()
};
