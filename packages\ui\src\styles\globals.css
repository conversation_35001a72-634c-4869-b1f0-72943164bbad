@import "tailwindcss";
@source "../../../../**/*.{ts,tsx}";

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.9973 0.0131 106.6348);
  --foreground: oklch(0.2046 0 0);
  --card: oklch(0.9978 0.0105 106.5881);
  --card-foreground: oklch(0.2046 0 0);
  --popover: oklch(0.9978 0.0105 106.5881);
  --popover-foreground: oklch(0.2046 0 0);
  --primary: oklch(0.4015 0.1296 291.4008);
  --primary-foreground: oklch(0.9835 0.0223 106.8022);
  --secondary: oklch(0.2046 0 0);
  --secondary-foreground: oklch(0.9835 0.0223 106.8022);
  --muted: oklch(0.9707 0.0095 299.2420);
  --muted-foreground: oklch(0.3600 0 0);
  --accent: oklch(0.9346 0.0166 301.2073);
  --accent-foreground: oklch(0.2046 0 0);
  --destructive: oklch(0.6280 0.2577 29.2339);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8975 0 0);
  --input: oklch(0.9633 0.0206 301.1491);
  --ring: oklch(0.6584 0.1196 294.8244);
  --chart-1: oklch(0.6584 0.1196 294.8244);
  --chart-2: oklch(0.5103 0 0);
  --chart-3: oklch(0.6830 0 0);
  --chart-4: oklch(0.8452 0 0);
  --chart-5: oklch(0.8975 0 0);
  --sidebar: oklch(0.9973 0.0131 106.6348);
  --sidebar-foreground: oklch(0.2046 0 0);
  --sidebar-primary: oklch(0.4015 0.1296 291.4008);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9346 0.0166 301.2073);
  --sidebar-accent-foreground: oklch(0.2046 0 0);
  --sidebar-border: oklch(0.8975 0 0);
  --sidebar-ring: oklch(0.6584 0.1196 294.8244);
  --font-sans: Poppins, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Geist Mono, monospace;
  --radius: 8px;
  --shadow-2xs: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.39);
  --shadow-xs: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.39);
  --shadow-sm: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.78), 0px 1px 2px -13.5px hsl(255.5556 42.8571% 37.0588% / 0.78);
  --shadow: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.78), 0px 1px 2px -13.5px hsl(255.5556 42.8571% 37.0588% / 0.78);
  --shadow-md: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.78), 0px 2px 4px -13.5px hsl(255.5556 42.8571% 37.0588% / 0.78);
  --shadow-lg: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.78), 0px 4px 6px -13.5px hsl(255.5556 42.8571% 37.0588% / 0.78);
  --shadow-xl: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.78), 0px 8px 10px -13.5px hsl(255.5556 42.8571% 37.0588% / 0.78);
  --shadow-2xl: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 1.95);
  --tracking-normal: -0.025em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2520 0 0);
  --foreground: oklch(0.9835 0.0223 106.8022);
  --card: oklch(0.2435 0 0);
  --card-foreground: oklch(0.9835 0.0223 106.8022);
  --popover: oklch(0.2520 0 0);
  --popover-foreground: oklch(0.9835 0.0223 106.8022);
  --primary: oklch(0.4015 0.1296 291.4008);
  --primary-foreground: oklch(0.9835 0.0223 106.8022);
  --secondary: oklch(0.2046 0 0);
  --secondary-foreground: oklch(0.9835 0.0223 106.8022);
  --muted: oklch(0.2957 0.0073 297.3872);
  --muted-foreground: oklch(0.8294 0.0164 106.7524);
  --accent: oklch(0.4359 0.0413 295.6440);
  --accent-foreground: oklch(0.9835 0.0223 106.8022);
  --destructive: oklch(0.6280 0.2577 29.2339);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3211 0 0);
  --input: oklch(0.3904 0 0);
  --ring: oklch(0.6584 0.1196 294.8244);
  --chart-1: oklch(0.6584 0.1196 294.8244);
  --chart-2: oklch(0.5103 0 0);
  --chart-3: oklch(0.6830 0 0);
  --chart-4: oklch(0.8452 0 0);
  --chart-5: oklch(0.8975 0 0);
  --sidebar: oklch(0.2393 0 0);
  --sidebar-foreground: oklch(0.9835 0.0223 106.8022);
  --sidebar-primary: oklch(0.4015 0.1296 291.4008);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.4359 0.0413 295.6440);
  --sidebar-accent-foreground: oklch(0.9835 0.0223 106.8022);
  --sidebar-border: oklch(0 0 0);
  --sidebar-ring: oklch(0.6584 0.1196 294.8244);
  --font-sans: Poppins, sans-serif;
  --font-serif: Playfair Display, serif;
  --font-mono: Geist Mono, monospace;
  --radius: 8px;
  --shadow-2xs: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.39);
  --shadow-xs: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.39);
  --shadow-sm: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.78), 0px 1px 2px -13.5px hsl(255.5556 42.8571% 37.0588% / 0.78);
  --shadow: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.78), 0px 1px 2px -13.5px hsl(255.5556 42.8571% 37.0588% / 0.78);
  --shadow-md: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.78), 0px 2px 4px -13.5px hsl(255.5556 42.8571% 37.0588% / 0.78);
  --shadow-lg: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.78), 0px 4px 6px -13.5px hsl(255.5556 42.8571% 37.0588% / 0.78);
  --shadow-xl: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 0.78), 0px 8px 10px -13.5px hsl(255.5556 42.8571% 37.0588% / 0.78);
  --shadow-2xl: 0px 0px 32px -12.5px hsl(255.5556 42.8571% 37.0588% / 1.95);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-poppins), ui-sans-serif, system-ui, sans-serif;
    font-weight: 600;
    letter-spacing: var(--tracking-normal);
  }
}