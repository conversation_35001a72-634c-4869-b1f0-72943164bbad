"use client";

import { useI18n } from "@curatd/shared/locales/client";
import CuratdLogo from "@curatd/ui/components/curatd-logo";
import { LandingHero } from "../landing-hero";

export function GymHeroSection() {
    const t = useI18n();


    return (
        <LandingHero
            title={t("landing.gym.hero.title")}
            subtitle={t("landing.gym.hero.subtitle")}
            ctaText={t("landing.gym.hero.cta")}
            backgroundImageSrc="https://images.pexels.com/photos/1954524/pexels-photo-1954524.jpeg"
            logo={<CuratdLogo size="extra-large" color="white" />}
        />
    );
} 