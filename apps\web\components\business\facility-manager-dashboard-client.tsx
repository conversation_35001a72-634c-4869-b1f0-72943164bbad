"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { StripeConnectCard } from "@/components/business/stripe-connect-card";
import {
  type UserResponse,
  type FacilityManagerProfile,
} from "@curatd/shared/backend/use-cases/user-management/core";

interface FacilityManagerDashboardClientProps {
  user: UserResponse;
  facilityManagerProfile: FacilityManagerProfile | null;
}

export function FacilityManagerDashboardClient({
  user,
  facilityManagerProfile,
}: FacilityManagerDashboardClientProps) {
  const searchParams = useSearchParams();

  useEffect(() => {
    if (searchParams.get("onboarded") === "true") {
      toast.success("Stripe account connected successfully!", {
        description: "Your account is now ready to receive payments.",
      });
    }
  }, [searchParams]);

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-2">Facility Manager Dashboard</h1>
      <p className="text-muted-foreground mb-8">
        Welcome, {user.display_name || user.email}!
      </p>

      <div className="grid gap-8 md:grid-cols-3">
        <div className="md:col-span-1">
          <StripeConnectCard
            profileType="facility_manager"
            stripeAccountId={facilityManagerProfile?.stripe_account_id}
            stripeAccountStatus={facilityManagerProfile?.stripe_account_status}
          />
        </div>
        <div className="md:col-span-2">
          {/* Other dashboard content will go here */}
        </div>
      </div>
    </div>
  );
}
