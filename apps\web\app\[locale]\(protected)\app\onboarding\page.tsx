"use client";

import { useState } from "react";
import { OnboardingFlow, type UserRole } from "@/components/onboarding";
import { toast } from "sonner";
import { useCurrentUserProfile } from "@curatd/shared/backend/use-cases/user-management/client";

export default function OnboardingPage() {
  const { error } = useCurrentUserProfile();
  const [isCompleting, setIsCompleting] = useState(false);

  const handleOnboardingComplete = async (selectedRole?: UserRole) => {
    if (!selectedRole) {
      toast.info("Please select a role to continue.");
      return;
    }

    setIsCompleting(true);

    try {
      const response = await fetch("/api/onboarding/complete", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ selectedRole }),
      });

      if (!response.ok) {
        const { error: apiError } = await response.json();
        throw new Error(apiError || "An unknown error occurred.");
      }

      toast.success("Onboarding completed! Redirecting...");
      window.location.href = "/app";
    } catch (err: unknown) {
      if (err instanceof Error) {
        toast.error(err.message || "Failed to complete onboarding.");
      } else {
        toast.error("Failed to complete onboarding.");
      }
      setIsCompleting(false);
    }
  };

  // If there was an error fetching the user, it's likely an auth issue.
  // The protected layout will handle the redirect to login. We render nothing here.
  if (error) {
    return null;
  }

  return (
    <OnboardingFlow
      onComplete={handleOnboardingComplete}
      className="w-full"
      isLoading={isCompleting}
    />
  );
}
