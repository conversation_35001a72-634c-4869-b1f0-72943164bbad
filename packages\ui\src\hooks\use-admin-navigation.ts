import { IconDashboard, IconUsers } from "@tabler/icons-react";
import { useCurrentLocale, useI18n } from "@curatd/shared/locales/client";

export function useAdminNavigation() {
    const t = useI18n();
    const locale = useCurrentLocale();

    const navItems = [
        {
            title: t("admin.nav.dashboard"),
            url: `/${locale}/`,
            icon: IconDashboard,
        },
        {
            title: t("admin.nav.users"),
            url: `/${locale}/users`,
            icon: IconUsers,
        },
    ];

    // Function to determine if a navigation item is active
    const isNavItemActive = (itemUrl: string, currentPathname: string): boolean => {
        // Exact match for dashboard (root)
        if (itemUrl === `/${locale}/` && currentPathname === `/${locale}`) {
            return true;
        }
        // For other pages, check if pathname starts with the nav URL
        if (itemUrl !== `/${locale}/` && currentPathname.startsWith(itemUrl)) {
            return true;
        }
        return false;
    };

    return {
        navItems,
        isNavItemActive,
    };
} 