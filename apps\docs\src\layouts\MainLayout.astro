---
import { ViewTransitions } from 'astro:transitions';

interface Props {
  title: string;
}

const { title } = Astro.props;
---

<!doctype html>
<html lang="en" class="light">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content="Curatd Developer Documentation" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Geist+Mono:wght@400;500&display=swap" rel="stylesheet">
    <meta name="generator" content={Astro.generator} />
    <title>{title ? `${title} - Curatd Docs` : 'Curatd Developer Documentation'}</title>
    <ViewTransitions />
    <style is:global>
      :root {
        --background: oklch(0.9973 0.0131 106.6348);
        --foreground: oklch(0.2046 0 0);
        --card: oklch(0.9978 0.0105 106.5881);
        --card-foreground: oklch(0.2046 0 0);
        --popover: oklch(0.9978 0.0105 106.5881);
        --popover-foreground: oklch(0.2046 0 0);
        --primary: oklch(0.4015 0.1296 291.4008);
        --primary-foreground: oklch(0.9835 0.0223 106.8022);
        --secondary: oklch(0.2046 0 0);
        --secondary-foreground: oklch(0.9835 0.0223 106.8022);
        --muted: oklch(0.9707 0.0095 299.2420);
        --muted-foreground: oklch(0.3600 0 0);
        --accent: oklch(0.9346 0.0166 301.2073);
        --accent-foreground: oklch(0.2046 0 0);
        --destructive: oklch(0.6280 0.2577 29.2339);
        --destructive-foreground: oklch(1.0000 0 0);
        --border: oklch(0.8975 0 0);
        --input: oklch(0.9633 0.0206 301.1491);
        --ring: oklch(0.6584 0.1196 294.8244);
        --sidebar: oklch(0.9973 0.0131 106.6348);
        --sidebar-foreground: oklch(0.2046 0 0);
        --sidebar-primary: oklch(0.4015 0.1296 291.4008);
        --sidebar-primary-foreground: oklch(1.0000 0 0);
        --sidebar-accent: oklch(0.9346 0.0166 301.2073);
        --sidebar-accent-foreground: oklch(0.2046 0 0);
        --sidebar-border: oklch(0.8975 0 0);
        --sidebar-ring: oklch(0.6584 0.1196 294.8244);
        --radius: 8px;
        --tracking-normal: -0.025em;
        --font-sans: 'Poppins', sans-serif;
        --font-serif: 'Playfair Display', serif;
        --font-mono: 'Geist Mono', monospace;
      }
      
      .dark {
        --background: oklch(0.2520 0 0);
        --foreground: oklch(0.9835 0.0223 106.8022);
        --card: oklch(0.2435 0 0);
        --card-foreground: oklch(0.9835 0.0223 106.8022);
        --popover: oklch(0.2520 0 0);
        --popover-foreground: oklch(0.9835 0.0223 106.8022);
        --primary: oklch(0.4015 0.1296 291.4008);
        --primary-foreground: oklch(0.9835 0.0223 106.8022);
        --secondary: oklch(0.2046 0 0);
        --secondary-foreground: oklch(0.9835 0.0223 106.8022);
        --muted: oklch(0.2957 0.0073 297.3872);
        --muted-foreground: oklch(0.8294 0.0164 106.7524);
        --accent: oklch(0.4359 0.0413 295.6440);
        --accent-foreground: oklch(0.9835 0.0223 106.8022);
        --destructive: oklch(0.6280 0.2577 29.2339);
        --destructive-foreground: oklch(1.0000 0 0);
        --border: oklch(0.3211 0 0);
        --input: oklch(0.3904 0 0);
        --ring: oklch(0.6584 0.1196 294.8244);
        --sidebar: oklch(0.2393 0 0);
        --sidebar-foreground: oklch(0.9835 0.0223 106.8022);
        --sidebar-primary: oklch(0.4015 0.1296 291.4008);
        --sidebar-primary-foreground: oklch(1.0000 0 0);
        --sidebar-accent: oklch(0.4359 0.0413 295.6440);
        --sidebar-accent-foreground: oklch(0.9835 0.0223 106.8022);
        --sidebar-border: oklch(0 0 0);
        --sidebar-ring: oklch(0.6584 0.1196 294.8244);
      }
      
      html {
        font-family: var(--font-sans);
        background: var(--background);
        color: var(--foreground);
        letter-spacing: var(--tracking-normal);
      }
      
      body {
        margin: 0;
        padding: 0;
        display: flex;
        min-height: 100vh;
        font-weight: 500;
      }
      
      .sidebar {
        width: 280px;
        background: var(--sidebar);
        color: var(--sidebar-foreground);
        padding: 1.5rem;
        box-sizing: border-box;
        border-right: 1px solid var(--sidebar-border);
      }
      
      .sidebar h2 {
        margin-top: 0;
        border-bottom: 1px solid var(--sidebar-border);
        padding-bottom: 0.75rem;
        font-weight: 600;
        color: var(--sidebar-primary);
      }
      
      .sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }
      
      .sidebar ul ul {
        padding-left: 1rem;
        margin-top: 0.5rem;
        margin-bottom: 0.75rem;
        border-left: 1px solid var(--sidebar-border);
      }
      
      .sidebar li {
        margin-bottom: 0.5rem;
      }
      
      .sidebar a {
        color: var(--sidebar-foreground);
        text-decoration: none;
        display: block;
        padding: 0.4rem 0.5rem;
        font-size: 0.95rem;
        border-radius: var(--radius);
        transition: all 0.2s ease;
      }
      
      .sidebar a:hover {
        color: var(--sidebar-primary);
        background: var(--sidebar-accent);
      }
      
      .content {
        flex-grow: 1;
        padding: 2rem;
        max-width: 960px;
        margin: 0 auto;
      }
      
      .content h1 {
        color: var(--foreground);
        margin-top: 0;
        margin-bottom: 1.5rem;
        border-bottom: 1px solid var(--border);
        padding-bottom: 0.75rem;
        font-family: var(--font-sans);
        font-weight: 700;
      }
      
      .content h2 {
        color: var(--foreground);
        margin-top: 2.5rem;
        margin-bottom: 1rem;
        font-family: var(--font-sans);
        font-weight: 600;
      }
      
      .content h3 {
        color: var(--foreground);
        margin-top: 2rem;
        margin-bottom: 0.75rem;
        font-family: var(--font-sans);
        font-weight: 600;
      }
      
      .content a {
        color: var(--primary);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
      }
      
      .content a:hover {
        text-decoration: underline;
      }
      
      .content code {
        background-color: var(--muted);
        color: var(--muted-foreground);
        padding: 0.2rem 0.4rem;
        border-radius: calc(var(--radius) - 4px);
        font-size: 0.85em;
        font-family: var(--font-mono);
        font-weight: 400;
      }
      
      .content pre {
        background-color: var(--card);
        color: var(--card-foreground);
        padding: 1rem;
        border-radius: var(--radius);
        overflow-x: auto;
        border: 1px solid var(--border);
        margin: 1.5rem 0;
      }
      
      .content pre code {
        background: transparent;
        padding: 0;
        color: inherit;
      }
      
      .content blockquote {
        border-left: 4px solid var(--primary);
        margin-left: 0;
        padding: 0.5rem 0 0.5rem 1rem;
        color: var(--muted-foreground);
        background: var(--muted);
        border-radius: 0 var(--radius) var(--radius) 0;
      }
      
      .content table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;
        border: 1px solid var(--border);
        border-radius: var(--radius);
        overflow: hidden;
      }
      
      .content th, 
      .content td {
        border: 1px solid var(--border);
        padding: 0.75rem;
        text-align: left;
      }
      
      .content th {
        background: var(--muted);
        color: var(--foreground);
        font-weight: 600;
      }
      
      .content ul, .content ol {
        padding-left: 1.5rem;
        margin: 1rem 0;
      }
      
      .content li {
        margin-bottom: 0.5rem;
      }
      
      .content p {
        margin-bottom: 1.25rem;
        line-height: 1.7;
      }
      
      .content img {
        max-width: 100%;
        border-radius: var(--radius);
      }
      
      .content hr {
        border: 0;
        height: 1px;
        background-color: var(--border);
        margin: 2rem 0;
      }
      
      .content strong {
        font-weight: 600;
      }
      
      /* Light/Dark mode toggle */
      .theme-toggle {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--muted);
        border: 1px solid var(--border);
        border-radius: var(--radius);
        padding: 0.5rem;
        cursor: pointer;
        transition: background 0.2s ease;
      }
      
      .theme-toggle:hover {
        background: var(--accent);
      }
      
      @media (max-width: 768px) {
        body {
          flex-direction: column;
        }
        
        .sidebar {
          width: 100%;
          padding: 1rem;
          border-right: none;
          border-bottom: 1px solid var(--sidebar-border);
        }
        
        .content {
          padding: 1.5rem 1rem;
        }
      }
    </style>
    <script>
      // Simple theme toggle functionality
      document.addEventListener('DOMContentLoaded', () => {
        const themeToggle = document.querySelector('.theme-toggle');
        if (themeToggle) {
          themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
          });
        }
        
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
          document.documentElement.className = savedTheme;
        }
      });
    </script>
  </head>
  <body>
    <nav class="sidebar">
      <h2>Curatd Docs</h2>
      <ul>
        <li><a href="/">Home</a></li>
        <li><a href="/backend">Backend</a>
          <ul>
            <li><a href="/backend/structure">Structure</a></li>
            <li><a href="/backend/schema">Database Schema</a></li>
            <li><a href="/backend/auth">Authentication</a></li>
            <li><a href="/backend/creating-use-cases">Creating Use Cases</a></li>
            <li><a href="/backend/function-calling">Function Calling</a></li>
            <li><a href="/backend/best-practices">Best Practices</a></li>
          </ul>
        </li>
        <!-- Add more navigation items as needed -->
      </ul>
    </nav>
    <button class="theme-toggle" aria-label="Toggle theme">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="5"></circle>
        <path d="M12 1v2M12 21v2M4.2 4.2l1.4 1.4M18.4 18.4l1.4 1.4M1 12h2M21 12h2M4.2 19.8l1.4-1.4M18.4 5.6l1.4-1.4"></path>
      </svg>
    </button>
    <main class="content">
      <slot />
    </main>
  </body>
</html>
