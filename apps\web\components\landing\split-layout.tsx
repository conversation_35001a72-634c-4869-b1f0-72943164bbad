"use client";

import { cn } from "@curatd/ui/lib/utils";
import { GalleryStack } from "@curatd/ui/components/gallery-stack";
import { useIsMobile } from "@curatd/ui/hooks/use-mobile";

interface SplitLayoutProps {
    index: number;
    title: string;
    subtitle: string;
    description: string;
    features: string[];
    images: { src: string }[];
}

export function SplitLayout({
    index,
    title,
    subtitle,
    description,
    features,
    images
}: SplitLayoutProps) {
    const isTextLeft = index % 2 === 0;
    const isMobile = useIsMobile();

    return (
        <section className="w-full">
            <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className={cn(
                    "grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 md:gap-16 lg:gap-20 xl:gap-24 items-start",
                    !isTextLeft && "lg:grid-flow-col-dense"
                )}>
                    {/* Text Content */}
                    <div className={cn(
                        "space-y-6 sm:space-y-8 md:space-y-10 md:py-8",
                        !isMobile && "sticky top-[30vh]",
                        !isTextLeft && "lg:col-start-2"
                    )}>
                        <div>
                            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-light mb-4 sm:mb-6 text-foreground tracking-wide">
                                {title}
                            </h2>
                            <div className="w-16 sm:w-20 md:w-24 h-px bg-border mb-4 sm:mb-6"></div>
                            <p className="text-lg sm:text-xl md:text-2xl font-light text-foreground mb-6 sm:mb-8 italic">
                                {subtitle}
                            </p>
                            <p className="text-base sm:text-lg text-muted-foreground leading-relaxed mb-8 sm:mb-10 md:mb-12">
                                {description}
                            </p>
                        </div>

                        {/* Features */}
                        <div className="space-y-4 sm:space-y-6">
                            <h3 className="text-xl sm:text-2xl font-medium text-foreground mb-4 sm:mb-6">
                                Key Features
                            </h3>
                            <div className="space-y-3 sm:space-y-4">
                                {features.map((feature, featureIndex) => (
                                    <div key={featureIndex} className="flex items-start space-x-3 sm:space-x-4">
                                        <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0 mt-2" />
                                        <span className="text-sm sm:text-base text-foreground font-light tracking-wide leading-relaxed">
                                            {feature}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Gallery Stack */}
                    <div className={cn(
                        "flex items-center justify-center min-h-[60vh] lg:min-h-[80vh]",
                        !isTextLeft && "lg:col-start-1"
                    )}>
                        <div className="w-full max-w-4xl">
                            <GalleryStack images={images} />
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
} 