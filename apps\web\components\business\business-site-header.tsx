"use client";

import { useCurrentLocale, useI18n } from "@curatd/shared/locales/client";
import { SiteHeaderBase } from "@curatd/ui/components/site-header-base";
import { useBusinessNavigation } from "@/hooks/use-business-navigation";

export function BusinessSiteHeader() {
  const locale = useCurrentLocale();
  const t = useI18n();
  const { navItems, isNavItemActive } = useBusinessNavigation();

  return (
    <SiteHeaderBase
      navItems={navItems}
      isNavItemActive={isNavItemActive}
      rootBreadcrumb={{
        title: t("business.nav.dashboard"),
        url: `/${locale}/app/business`,
      }}
    />
  );
}
