import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { ThemeProvider } from "@curatd/ui/components/theme-provider";
import { Toaster } from "@curatd/ui/components/sonner";
import { SWRProvider } from "../components/swr-provider";
import "@curatd/ui/globals.css";
import AuthServerProvider from "@curatd/ui/components/auth-server-provider";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: {
    default: "Curatd - Private Wellness Platform",
    template: "%s | Curatd",
  },
  description:
    "Curatd is a platform for connecting coaches, gyms, and customers. Private wellness. Anywhere, anytime. Book personal sessions at home, outdoors, or in select partner gyms.",
  keywords: [
    "Private wellness",
    "Personal training",
    "Fitness coaching",
    "Home workouts",
    "Gym sessions",
    "Outdoor fitness",
    "Personal trainer",
    "Fitness platform",
    "Wellness coaching",
    "Online booking",
    "Fitness app",
    "Private sessions",
  ],
  authors: [{ name: "The Tech Nation" }],
  creator: "<PERSON>uratd",
  publisher: "Curatd",
  applicationName: "Curatd",
  category: "Health & Fitness",
  classification: "Fitness Platform",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    title: "Curatd - Private Wellness Platform",
    description:
      "Curatd is a platform for connecting coaches, gyms, and customers. Private wellness. Anywhere, anytime. Book personal sessions at home, outdoors, or in select partner gyms.",
    url: "https://curatd.com/",
    siteName: "Curatd",
    images: [
      {
        url: "https://curatd.com/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Curatd platform preview",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Curatd - Private Wellness Platform",
    description:
      "Curatd is a platform for connecting coaches, gyms, and customers. Private wellness. Anywhere, anytime. Book personal sessions at home, outdoors, or in select partner gyms.",
    images: ["https://curatd.com/og-image.jpg"],
    site: "@curatdapp",
    creator: "@curatdapp",
  },
  alternates: {
    canonical: "https://curatd.com/",
    languages: {
      en: "https://curatd.com/en",
      fr: "https://curatd.com/fr",
      pt: "https://curatd.com/pt",
    },
  },
  verification: {
    google: "your-google-site-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <AuthServerProvider>
      <html lang="en" suppressHydrationWarning>
        <head>
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0"
          />
          <link rel="icon" href="/favicon.ico" sizes="any" />
          <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
          <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
          <link rel="manifest" href="/manifest.json" />
          <meta name="theme-color" content="#000000" />
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                "@context": "https://schema.org",
                "@type": "Organization",
                name: "Curatd",
                url: "https://curatd.com/",
                logo: "https://curatd.com/og-image.jpg",
                sameAs: ["https://twitter.com/curatdapp"],
              }),
            }}
          />
        </head>
        <body
          className={`${poppins.variable} antialiased bg-background text-foreground`}
        >
          <ThemeProvider>
            <SWRProvider>{children}</SWRProvider>
            <Toaster />
          </ThemeProvider>
        </body>
      </html>
    </AuthServerProvider>
  );
}
