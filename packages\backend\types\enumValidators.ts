import { v } from "convex/values";
import {
    Roles,
    InvitationStatus,
    DocumentVerificationType,
    FacilityType,
    SubscriptionStatus,
    ProductVariantType,
    AddonsOwnerType,
    OrderStatus,
    PaymentStatus,
    DiscountMethod,
    NotificationStatus,
    WaitingListIntent,
    StripeConnectStatus
} from "./enums";

/*──────────────────────── ENUMS VALIDATORS ────────────────────────*/
/**
 * This section contains all the enums validators for the schema. They are used as enums to be able to
 * reuse them both in the schema and in the functions to ensure type safety.
 */

/*──────────────────────── ROLES VALIDATORS ────────────────────────*/

export const adminRoleValidator = v.literal(Roles.ADMIN);
export const facilityManagerRoleValidator = v.literal(Roles.FACILITY_MANAGER);
export const coachRoleValidator = v.literal(Roles.COACH);
export const customerRoleValidator = v.literal(Roles.CUSTOMER);


export const roleValidator = v.union(
    adminRoleValidator,
    facilityManagerRoleValidator,
    coachRoleValidator,
    customerRoleValidator
);

/*──────────────────────── INVITATION STATUS VALIDATORS ──────────*/

export const invitationStatusPendingValidator = v.literal(InvitationStatus.PENDING);
export const invitationStatusAcceptedValidator = v.literal(InvitationStatus.ACCEPTED);
export const invitationStatusExpiredValidator = v.literal(InvitationStatus.EXPIRED);
export const invitationStatusCancelledValidator = v.literal(InvitationStatus.CANCELLED);

export const invitationStatusValidator = v.union(
    invitationStatusPendingValidator,
    invitationStatusAcceptedValidator,
    invitationStatusExpiredValidator,
    invitationStatusCancelledValidator
);

/*──────────────────────── DOCUMENT VERIFICATION TYPES VALIDATORS ──────────────*/

export const documentVerificationTypePassportValidator = v.literal(DocumentVerificationType.PASSPORT);
export const documentVerificationTypeIdCardValidator = v.literal(DocumentVerificationType.ID_CARD);
export const documentVerificationTypeDrivingLicenseValidator = v.literal(DocumentVerificationType.DRIVING_LICENSE);
export const documentVerificationTypeResidencePermitValidator = v.literal(DocumentVerificationType.RESIDENCE_PERMIT);

export const documentVerificationTypeValidator = v.union(
    documentVerificationTypePassportValidator,
    documentVerificationTypeIdCardValidator,
    documentVerificationTypeDrivingLicenseValidator,
    documentVerificationTypeResidencePermitValidator
);

/*──────────────────────── FACILITY TYPES VALIDATORS ──────────────*/

export const facilityTypeGymValidator = v.literal(FacilityType.GYM);
export const facilityTypeStadiumValidator = v.literal(FacilityType.STADIUM);
export const facilityTypeCourtValidator = v.literal(FacilityType.COURT);
export const facilityTypePoolValidator = v.literal(FacilityType.POOL);
export const facilityTypeOtherValidator = v.literal(FacilityType.OTHER);

export const facilityTypeValidator = v.union(
    facilityTypeGymValidator,
    facilityTypeStadiumValidator,
    facilityTypeCourtValidator,
    facilityTypePoolValidator,
    facilityTypeOtherValidator
);

/*──────────────────────── SUBSCRIPTION STATUS VALIDATORS ──────────────*/

export const subscriptionStatusActiveValidator = v.literal(SubscriptionStatus.ACTIVE);
export const subscriptionStatusCancelledValidator = v.literal(SubscriptionStatus.CANCELLED);

export const subscriptionStatusValidator = v.union(
    subscriptionStatusActiveValidator,
    subscriptionStatusCancelledValidator
);

/*──────────────────────── PRODUCT VARIANT TYPES VALIDATORS ──────────────*/

export const productVariantTypePrivateValidator = v.literal(ProductVariantType.PRIVATE);

export const productVariantTypeBuddyValidator = v.literal(ProductVariantType.BUDDY);
export const productVariantTypeGroupValidator = v.literal(ProductVariantType.GROUP);

export const productVariantTypeValidator = v.union(
    productVariantTypePrivateValidator,
    productVariantTypeBuddyValidator,
    productVariantTypeGroupValidator
);

/*──────────────────────── ADDONS OWNER TYPES VALIDATORS ──────────────*/

export const addonsOwnerTypeProductVariantValidator = v.literal(AddonsOwnerType.PRODUCT_VARIANT);
export const addonsOwnerTypeFacilityAssetValidator = v.literal(AddonsOwnerType.FACILITY_ASSET);

export const addonsOwnerTypeValidator = v.union(
    addonsOwnerTypeProductVariantValidator,
    addonsOwnerTypeFacilityAssetValidator
);

/*──────────────────────── ORDER STATUS VALIDATORS ──────────────*/

export const orderStatusPendingValidator = v.literal(OrderStatus.PENDING);
export const orderStatusConfirmedValidator = v.literal(OrderStatus.CONFIRMED);
export const orderStatusCancelledValidator = v.literal(OrderStatus.CANCELLED);
export const orderStatusCompletedValidator = v.literal(OrderStatus.COMPLETED);

export const orderStatusValidator = v.union(
    orderStatusPendingValidator,
    orderStatusConfirmedValidator,
    orderStatusCancelledValidator,
    orderStatusCompletedValidator
);

/*──────────────────────── PAYMENT STATUS VALIDATORS ──────────────*/

export const paymentStatusUnpaidValidator = v.literal(PaymentStatus.UNPAID);
export const paymentStatusPaidValidator = v.literal(PaymentStatus.PAID);
export const paymentStatusFailedValidator = v.literal(PaymentStatus.FAILED);

export const paymentStatusValidator = v.union(
    paymentStatusUnpaidValidator,
    paymentStatusPaidValidator,
    paymentStatusFailedValidator
);

/*──────────────────────── DISCOUNT METHODS VALIDATORS ──────────────*/

export const discountMethodPercentageValidator = v.literal(DiscountMethod.PERCENTAGE);
export const discountMethodFixedValidator = v.literal(DiscountMethod.FIXED);
export const discountMethodBuyXGetYValidator = v.literal(DiscountMethod.BUY_X_GET_Y);

export const discountMethodValidator = v.union(
    discountMethodPercentageValidator,
    discountMethodFixedValidator,
    discountMethodBuyXGetYValidator
);

/*──────────────────────── NOTIFICATION STATUS VALIDATORS ──────────────*/

export const notificationStatusQueuedValidator = v.literal(NotificationStatus.QUEUED);
export const notificationStatusSentValidator = v.literal(NotificationStatus.SENT);
export const notificationStatusDeliveredValidator = v.literal(NotificationStatus.DELIVERED);
export const notificationStatusReadValidator = v.literal(NotificationStatus.READ);
export const notificationStatusFailedValidator = v.literal(NotificationStatus.FAILED);

export const notificationStatusValidator = v.union(
    notificationStatusQueuedValidator,
    notificationStatusSentValidator,
    notificationStatusDeliveredValidator,
    notificationStatusReadValidator,
    notificationStatusFailedValidator
);

/*──────────────────────── WAITING LIST INTENT VALIDATORS ──────────────*/

export const waitingListIntentCoachInterestValidator = v.literal(WaitingListIntent.COACH_INTEREST);

export const waitingListIntentFacilityManagerInterestValidator = v.literal(WaitingListIntent.FACILITY_MANAGER_INTEREST);
export const waitingListIntentCustomerInterestValidator = v.literal(WaitingListIntent.CUSTOMER_INTEREST);
export const waitingListIntentNewsletterValidator = v.literal(WaitingListIntent.NEWSLETTER);
export const waitingListIntentBetaFeatureValidator = v.literal(WaitingListIntent.BETA_FEATURE);

export const waitingListIntentValidator = v.union(
    waitingListIntentCoachInterestValidator,
    waitingListIntentFacilityManagerInterestValidator,
    waitingListIntentCustomerInterestValidator,
    waitingListIntentNewsletterValidator,
    waitingListIntentBetaFeatureValidator
);

/*──────────────────────── STRIPE CONNECT STATUS AND ID VALIDATORS ──────────────*/

export const stripeConnectStatusActiveValidator = v.literal(StripeConnectStatus.ACTIVE);
export const stripeConnectStatusOnboardingValidator = v.literal(StripeConnectStatus.ONBOARDING);
export const stripeConnectStatusRestrictedValidator = v.literal(StripeConnectStatus.RESTRICTED);
export const stripeConnectStatusRestrictedSoonValidator = v.literal(StripeConnectStatus.RESTRICTED_SOON);
export const stripeConnectStatusInReviewValidator = v.literal(StripeConnectStatus.IN_REVIEW);
export const stripeConnectStatusRejectedValidator = v.literal(StripeConnectStatus.REJECTED);
export const stripeConnectStatusInactiveValidator = v.literal(StripeConnectStatus.INACTIVE);
export const stripeConnectStatusNotConnectedValidator = v.literal(StripeConnectStatus.NOT_CONNECTED);

export const stripeConnectStatusValidator = v.union(
    stripeConnectStatusActiveValidator,
    stripeConnectStatusOnboardingValidator,
    stripeConnectStatusRestrictedValidator,
    stripeConnectStatusRestrictedSoonValidator,
    stripeConnectStatusInReviewValidator,
    stripeConnectStatusRejectedValidator,
    stripeConnectStatusInactiveValidator,
    stripeConnectStatusNotConnectedValidator
);

export const stripeConnectValidator = {
    stripeConnectStatus: v.optional(stripeConnectStatusValidator),
    stripeConnectId: v.optional(v.string())
}