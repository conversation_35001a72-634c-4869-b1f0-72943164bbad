import * as React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@curatd/ui/components/dialog";
import { Button } from "@curatd/ui/components/button";
import { Checkbox } from "@curatd/ui/components/checkbox";
import { toast } from "sonner";
import { useI18n } from "@curatd/shared/locales/client";
import { useMutation } from "convex/react";
import { Enums } from "@curatd/backend";
import { api } from "@curatd/backend/api";
import { Doc } from "@curatd/backend/schema";

const USER_ROLES = Object.values(Enums.Roles);

// Updated type to match the user data from Convex
type UserRowType = Pick<Doc<"users">, "_id" | "name" | "email" | "roles">;

interface ManageRolesDialogProps {
  user: UserRowType | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function ManageRolesDialog({
  user,
  open,
  onOpenChange,
  onSuccess,
}: ManageRolesDialogProps) {
  const t = useI18n();

  const assignRolesMutation = useMutation(
    api.functions.useCases.admin.userManagement.users.assignRoles
  );
  const removeRolesMutation = useMutation(
    api.functions.useCases.admin.userManagement.users.revokeRoles
  );

  const [selectedRoles, setSelectedRoles] = React.useState<Enums.Roles[]>([]);
  const [isSaving, setIsSaving] = React.useState(false);

  React.useEffect(() => {
    if (user) {
      // Use the roles directly from the user object
      setSelectedRoles(user.roles || []);
    }
  }, [user]);

  if (!user) {
    return null;
  }

  const handleSave = async () => {
    setIsSaving(true);
    const initialRoles = user.roles || [];

    const rolesToAdd = selectedRoles.filter(
      (role) => !initialRoles.includes(role)
    );
    const rolesToRemove = initialRoles.filter(
      (role) => !selectedRoles.includes(role)
    );

    try {
      // Add new roles
      if (rolesToAdd.length > 0) {
        await assignRolesMutation({
          user_id: user._id,
          roles: rolesToAdd,
        });
      }

      // Remove roles
      if (rolesToRemove.length > 0) {
        await removeRolesMutation({
          user_id: user._id,
          roles: rolesToRemove,
        });
      }

      toast.success(t("admin.userManagement.manageRolesDialog.successMessage"));
      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to update roles:", error);
      toast.error(t("admin.userManagement.manageRolesDialog.errorMessage"));
    } finally {
      setIsSaving(false);
    }
  };

  const onRoleToggle = (checked: boolean, roleCode: Enums.Roles) => {
    setSelectedRoles((prev) =>
      checked ? [...prev, roleCode] : prev.filter((code) => code !== roleCode)
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {t("admin.userManagement.manageRolesDialog.title", {
              userName: user.name || user.email,
            })}
          </DialogTitle>
          <DialogDescription>
            {t("admin.userManagement.manageRolesDialog.description")}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {USER_ROLES?.map((role) => (
            <div key={role} className="flex items-center space-x-3">
              <Checkbox
                id={`role-${role}`}
                checked={selectedRoles.includes(role)}
                onCheckedChange={(checked) => onRoleToggle(!!checked, role)}
              />
              <label
                htmlFor={`role-${role}`}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {role}
              </label>
            </div>
          ))}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("admin.userManagement.manageRolesDialog.cancel")}
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving
              ? t("admin.userManagement.manageRolesDialog.saving")
              : t("admin.userManagement.manageRolesDialog.saveChanges")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
