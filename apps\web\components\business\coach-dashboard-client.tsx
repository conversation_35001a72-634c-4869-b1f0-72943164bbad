"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { StripeConnectCard } from "@/components/business/stripe-connect-card";
import {
  type UserResponse,
  type CoachProfile,
} from "@curatd/shared/backend/use-cases/user-management/core";

interface CoachDashboardClientProps {
  user: UserResponse;
  coachProfile: CoachProfile | null;
}

export function CoachDashboardClient({
  user,
  coachProfile,
}: CoachDashboardClientProps) {
  const searchParams = useSearchParams();

  useEffect(() => {
    if (searchParams.get("onboarded") === "true") {
      toast.success("Stripe account connected successfully!", {
        description: "Your account is now ready to receive payments.",
      });
    }
  }, [searchParams]);

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-2">Coach Dashboard</h1>
      <p className="text-muted-foreground mb-8">
        Welcome, {user.display_name || user.email}!
      </p>

      <div className="grid gap-8 md:grid-cols-3">
        <div className="md:col-span-1">
          <StripeConnectCard
            profileType="coach"
            stripeAccountId={coachProfile?.stripe_account_id}
            stripeAccountStatus={coachProfile?.stripe_account_status}
          />
        </div>
        <div className="md:col-span-2">
          {/* Other dashboard content will go here */}
        </div>
      </div>
    </div>
  );
}
