"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { useI18n } from "@curatd/shared/locales/client";

import { Button } from "@curatd/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@curatd/ui/components/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@curatd/ui/components/form";
import { Input } from "@curatd/ui/components/input";
import CuratdLogo from "@curatd/ui/components/curatd-logo";

type AcceptInvitationForm = {
  password: string;
  confirmPassword: string;
  displayName?: string;
};

export default function CompleteInvitePage() {
  const t = useI18n();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [sessionSet, setSessionSet] = React.useState(false);

  const acceptInvitationSchema = z
    .object({
      password: z
        .string()
        .min(6, t("admin.acceptInvitation.passwordMinLength")),
      confirmPassword: z.string(),
      displayName: z.string().optional(),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t("admin.acceptInvitation.passwordsDoNotMatch"),
      path: ["confirmPassword"],
    });

  const form = useForm<AcceptInvitationForm>({
    resolver: zodResolver(acceptInvitationSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
      displayName: "",
    },
  });

  React.useEffect(() => {
    const hash = window.location.hash.substring(1);
    const params = new URLSearchParams(hash);

    const access_token = params.get("access_token");
    const refresh_token = params.get("refresh_token");

    if (!access_token || !refresh_token) {
      toast.error(t("admin.acceptInvitation.invalidToken"));
      router.replace("/login");
      return;
    }

    // TODO: Implement auth

    (async () => {
      // TODO: Implement accept invitation
      // if (error) {
      //   toast.error(t("admin.acceptInvitation.failedToAccept"));
      //   router.replace("/login");
      //   return;
      // }
      // setSessionSet(true);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmit = async (data: AcceptInvitationForm) => {
    setIsSubmitting(true);
    try {
      // TODO: Implement accept invitation
      // if (error) {
      //   throw error;
      // }
      // toast.success(t("admin.acceptInvitation.success"));
      // router.replace("/");
    } catch (error) {
      // console.error("Failed to set password:", error);
      // toast.error(
      //   error instanceof Error
      //     ? error.message
      //     : t("admin.acceptInvitation.failedToAccept")
      // );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!sessionSet) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">
            {t("admin.acceptInvitation.loading")}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <CuratdLogo className="h-8 w-auto" />
          </div>
          <CardTitle className="text-2xl font-bold">
            {t("admin.acceptInvitation.title")}
          </CardTitle>
          <CardDescription>
            {t("admin.acceptInvitation.description")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit)}
              className="space-y-4"
            >
              <FormField
                control={form.control}
                name="displayName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("admin.acceptInvitation.displayNameLabel")}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t(
                          "admin.acceptInvitation.displayNamePlaceholder"
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("admin.acceptInvitation.passwordLabel")} *
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder={t(
                          "admin.acceptInvitation.passwordPlaceholder"
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("admin.acceptInvitation.confirmPasswordLabel")} *
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder={t(
                          "admin.acceptInvitation.confirmPasswordPlaceholder"
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting
                  ? t("admin.acceptInvitation.accepting")
                  : t("admin.acceptInvitation.acceptButton")}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
