{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "globalEnv": ["NODE_ENV", "CONVEX_DEPLOYMENT", "CONVEX_URL", "NEXT_PUBLIC_CONVEX_URL", "EXPO_PUBLIC_CONVEX_URL", "STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_SECRET", "STRIPE_CLIENT_ID", "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY", "EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**", ".astro/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "start": {"cache": false, "persistent": true}, "clean": {"cache": false}}}