"use client";

import React from "react";
import { useI18n } from "@curatd/shared/locales/client";
import { HandshakeIcon, BookCheckIcon, SquarePenIcon, BicepsFlexedIcon } from "lucide-react";
import { HowItWorksSectionGeneric } from "./how-it-works-section-generic";

export function HowItWorksSection() {
    const t = useI18n();
    const steps = [
        {
            number: "01",
            icon: <HandshakeIcon className="w-8 h-8 text-foreground" />,
            title: t("landing.howItWorks.steps.selectSportDuration.title"),
            description: t("landing.howItWorks.steps.selectSportDuration.description")
        },
        {
            number: "02",
            icon: <BookCheckIcon className="w-8 h-8 text-foreground" />,
            title: t("landing.howItWorks.steps.chooseCoach.title"),
            description: t("landing.howItWorks.steps.chooseCoach.description")
        },
        {
            number: "03",
            icon: <SquarePenIcon className="w-8 h-8 text-foreground" />,
            title: t("landing.howItWorks.steps.bookLocation.title"),
            description: t("landing.howItWorks.steps.bookLocation.description")
        },
        {
            number: "04",
            icon: <BicepsFlexedIcon className="w-8 h-8 text-foreground" />,
            title: t("landing.howItWorks.steps.payAndGo.title"),
            description: t("landing.howItWorks.steps.payAndGo.description")
        }
    ];

    return (
        <HowItWorksSectionGeneric
            title={t("landing.howItWorks.title")}
            steps={steps}
            columns={4}
        />
    );
} 