import Image from "next/image";
import { cn } from "../lib/utils";

const FeatureImage = ({ title, description, subtitle, image, left }: { title: string, description: string, subtitle: string, image: string, left: boolean }) => {
    return (
        <div className={cn(
            "flex bg-card overflow-clip md:col-span-2 md:grid md:grid-cols-2",
            left ? "flex-col-reverse md:flex-row" : "flex-col md:flex-row"
        )}>
            <div className={cn(
                "flex flex-col justify-center px-6 py-8 md:px-8 md:py-10 lg:px-10 lg:py-12",
                left && "md:order-2"
            )}>
                <h3 className="mb-3 text-xl font-bold md:mb-4 md:text-4xl lg:mb-6">
                    {title}
                </h3>
                <p className="text-muted-foreground md:text-2xl">
                    {description}
                </p>
                <p className="text-muted-foreground md:text-xl mt-6">
                    {subtitle}
                </p>
            </div>
            <div className={cn(
                "md:min-h-[24rem] lg:min-h-[28rem] xl:min-h-[32rem]",
                left && "md:order-1"
            )}>
                <Image
                    width={500}
                    height={500}
                    src={image}
                    alt={title}
                    className="aspect-16/9 h-full w-full object-cover object-center"
                />
            </div>
        </div>
    );
};

export { FeatureImage };
