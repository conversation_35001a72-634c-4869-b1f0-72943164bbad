import { redirect } from "next/navigation";
import { getCurrentUser } from "@curatd/shared/backend/use-cases/user-management/server";

export default async function BusinessHomePage() {
  // This page acts as a router for business users.
  // It checks the user's roles and redirects them to their primary dashboard.
  const user = await getCurrentUser();
  const roles = user.user_roles.map((ur) => ur.roles.code);

  // Prioritize coach dashboard if user has both roles
  if (roles.includes("coach")) {
    return redirect("/app/business/coach");
  }

  if (roles.includes("facility_manager")) {
    return redirect("/app/business/facility-manager");
  }

  // Fallback in case a business user has no recognized business role.
  // This should not be reached in a normal flow.
  return redirect("/app");
}
