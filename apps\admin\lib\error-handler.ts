import type { useI18n } from "@curatd/shared/locales/client";

/**
 * Error response interface from API
 */
export interface APIErrorResponse {
    success: false;
    error: string;
    code?: string;
    details?: any;
}

/**
 * Error info for processing
 */
export interface ErrorInfo {
    name?: string;
    code?: string;
    error?: string;
    status?: number;
    message?: string;
}

/**
 * Error handler options for invite dialog
 */
export interface InviteDialogErrorHandlerOptions {
    /** Translation function */
    t: ReturnType<typeof useI18n>;
    /** Whether to log errors to console */
    logErrors?: boolean;
}

/**
 * Comprehensive error handler for invite dialog API responses
 * Provides specific error messages based on error codes, status codes, and error content
 */
export class InviteDialogErrorHandler {
    private t: ReturnType<typeof useI18n>;
    private logErrors: boolean;

    constructor(options: InviteDialogErrorHandlerOptions) {
        this.t = options.t;
        this.logErrors = options.logErrors ?? true;
    }

    /**
     * Get error message from error info and response
     */
    getErrorMessage(error: any, response?: Response): string {
        if (this.logErrors) {
            console.error('InviteDialog error:', error, response);
        }

        // Network errors
        if (!response || error.name === 'TypeError' || error.name === 'NetworkError') {
            return this.t("admin.userManagement.inviteDialog.errors.networkError");
        }

        // Server returned an error response
        if (response && !response.ok) {
            const statusCode = response.status;

            // Handle specific HTTP status codes
            switch (statusCode) {
                case 401:
                    return this.t("admin.userManagement.inviteDialog.errors.authenticationRequired");
                case 403:
                    return this.t("admin.userManagement.inviteDialog.errors.insufficientPermissions");
                case 404:
                    return this.t("admin.userManagement.inviteDialog.errors.notFound");
                case 409:
                    return this.t("admin.userManagement.inviteDialog.errors.userAlreadyExists");
                case 422:
                    return this.t("admin.userManagement.inviteDialog.errors.validationError");
                case 429:
                    return this.t("admin.userManagement.inviteDialog.errors.rateLimitExceeded");
                case 500:
                case 502:
                case 503:
                case 504:
                    return this.t("admin.userManagement.inviteDialog.errors.serverError");
                case 400:
                    // For 400 errors, check the error code for more specific messages
                    if (error.code) {
                        return this.getErrorMessageByCode(error.code, error.error);
                    }
                    return this.t("admin.userManagement.inviteDialog.errors.validationError");
                default:
                    return this.t("admin.userManagement.inviteDialog.errors.serverError");
            }
        }

        // Handle errors with codes but no response (e.g., from catch blocks)
        if (error.code) {
            return this.getErrorMessageByCode(error.code, error.error || error.message);
        }

        // Fallback for any other errors
        return this.t("admin.userManagement.inviteDialog.errors.genericError");
    }

    /**
     * Get error message based on error code
     */
    private getErrorMessageByCode(code: string, errorMessage?: string): string {
        switch (code) {
            case 'VALIDATION_ERROR':
                // Check for specific validation error patterns
                if (errorMessage) {
                    if (errorMessage.includes('user with this email already exists') ||
                        errorMessage.includes('A user with this email already exists')) {
                        return this.t("admin.userManagement.inviteDialog.errors.userAlreadyExists");
                    }
                    if (errorMessage.includes('invalid email') || errorMessage.includes('Invalid email')) {
                        return this.t("admin.userManagement.inviteDialog.errors.invalidEmail");
                    }
                    if (errorMessage.includes('role') && errorMessage.includes('invalid')) {
                        return this.t("admin.userManagement.inviteDialog.errors.invalidRoles");
                    }
                    if (errorMessage.includes('At least one role must be specified') ||
                        errorMessage.includes('Please select at least one role')) {
                        return this.t("admin.userManagement.inviteDialog.errors.missingRoles");
                    }
                }
                return this.t("admin.userManagement.inviteDialog.errors.validationError");

            case 'SUPABASE_ERROR':
                // Check for specific Supabase error patterns
                if (errorMessage) {
                    if (errorMessage.includes('Failed to send invitation email') ||
                        errorMessage.includes('email') ||
                        errorMessage.includes('SMTP') ||
                        errorMessage.includes('mail')) {
                        return this.t("admin.userManagement.inviteDialog.errors.emailConfigurationError");
                    }
                    if (errorMessage.includes('SUPABASE_SERVICE_ROLE_KEY') ||
                        errorMessage.includes('service role key')) {
                        return this.t("admin.userManagement.inviteDialog.errors.configurationError");
                    }
                }
                return this.t("admin.userManagement.inviteDialog.errors.supabaseError");

            case 'AUTHORIZATION_ERROR':
                return this.t("admin.userManagement.inviteDialog.errors.insufficientPermissions");

            case 'NOT_FOUND':
                if (errorMessage && errorMessage.includes('role')) {
                    return this.t("admin.userManagement.inviteDialog.errors.invalidRoles");
                }
                return this.t("admin.userManagement.inviteDialog.errors.notFound");

            case 'UNKNOWN_ERROR':
                return this.t("admin.userManagement.inviteDialog.errors.genericError");

            default:
                return this.t("admin.userManagement.inviteDialog.errors.genericError");
        }
    }

    /**
     * Handle API response and extract error information
     */
    async handleAPIResponse<T>(response: Response): Promise<T> {
        let result: any;

        try {
            result = await response.json();
        } catch (parseError) {
            // If we can't parse the response, it's likely a server error
            throw new Error(this.t("admin.userManagement.inviteDialog.errors.serverError"));
        }

        if (!response.ok) {
            // Create an error object with detailed information for error handling
            const errorInfo: ErrorInfo = {
                name: 'APIError',
                code: result.code,
                error: result.error,
                status: response.status,
            };

            const errorMessage = this.getErrorMessage(errorInfo, response);
            throw new Error(errorMessage);
        }

        return result;
    }

    /**
     * Handle fetch errors
     */
    handleFetchError(error: any): never {
        let errorMessage: string;

        if (error instanceof Error) {
            // If it's already a formatted error message, use it
            if (error.message.includes("admin.userManagement.inviteDialog.errors.")) {
                errorMessage = error.message;
            } else {
                // Otherwise, determine the appropriate error message
                errorMessage = this.getErrorMessage(error);
            }
        } else {
            errorMessage = this.t("admin.userManagement.inviteDialog.errors.genericError");
        }

        throw new Error(errorMessage);
    }
}

/**
 * Factory function to create error handler for invite dialog
 */
export function createInviteDialogErrorHandler(options: InviteDialogErrorHandlerOptions): InviteDialogErrorHandler {
    return new InviteDialogErrorHandler(options);
}

/**
 * Common error patterns for matching
 */
export const ERROR_PATTERNS = {
    USER_EXISTS: [
        'user with this email already exists',
        'A user with this email already exists',
        'email already registered',
        'duplicate email'
    ],
    INVALID_EMAIL: [
        'invalid email',
        'Invalid email',
        'malformed email',
        'email format'
    ],
    EMAIL_SERVICE: [
        'Failed to send invitation email',
        'Failed to send email',
        'SMTP error',
        'mail service',
        'email delivery'
    ],
    MISSING_ROLES: [
        'At least one role must be specified',
        'Please select at least one role',
        'No roles selected',
        'role required'
    ],
    INVALID_ROLES: [
        'role',
        'invalid',
        'not found',
        'does not exist'
    ]
} as const; 