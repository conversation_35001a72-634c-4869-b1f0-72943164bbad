import { NextResponse } from "next/server";
import { userManagementServer } from "@curatd/shared/backend/use-cases/user-management/server";
import {
    AssignOnboardingRoleInput,
    UserManagementError,
} from "@curatd/shared/backend/use-cases/user-management/core";

export async function POST(request: Request) {
    try {
        const body = await request.json();
        const { selectedRole } = body;

        if (!selectedRole) {
            return NextResponse.json(
                { error: "selectedRole is required" },
                { status: 400 }
            );
        }

        let roleCode: 'customer' | 'coach' | 'facility_manager';
        switch (selectedRole) {
            case "regular_user":
                roleCode = "customer";
                break;
            case "coach":
                roleCode = "coach";
                break;
            case "facility_manager":
                roleCode = "facility_manager";
                break;
            default:
                return NextResponse.json(
                    { error: "Invalid role specified" },
                    { status: 400 }
                );
        }

        const input: AssignOnboardingRoleInput = { roleCode };
        const updatedUser = await userManagementServer.assignOnboardingRole(input);

        return NextResponse.json(updatedUser);
    } catch (error) {
        console.error("[ONBOARDING_COMPLETE_POST]", error);

        if (error instanceof UserManagementError) {
            return NextResponse.json(
                { error: error.message, code: error.code },
                { status: error.statusCode }
            );
        }

        return new NextResponse("Internal Server Error", { status: 500 });
    }
} 