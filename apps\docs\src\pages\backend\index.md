---
title: Backend Documentation
layout: ../../layouts/MainLayout.astro
---

# Curatd Backend Architecture

This documentation covers the backend architecture for the Curatd platform, which is built using Convex as the primary backend service.

## Overview

The Curatd backend is structured as a monorepo package using Convex, a serverless database platform that provides real-time data storage and synchronization. The backend is organized with a domain-driven design approach, focusing on use cases and clear separation of concerns.

### Key Technologies

- **Convex**: Serverless backend with real-time database capabilities
- **TypeScript**: Strongly-typed language for all backend code
- **Zod/Convex Validators**: Data validation for backend functions
- **Auth**: Custom authentication system with role-based access control

## Quick Links

- [Backend Structure](/backend/structure)
- [Database Schema](/backend/schema)
- [Authentication & Authorization](/backend/auth)
- [Creating New Use Cases](/backend/creating-use-cases)
- [Function Calling](/backend/function-calling)
- [Best Practices](/backend/best-practices)
