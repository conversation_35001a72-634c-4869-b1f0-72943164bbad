"use client";

import { useI18n } from "@curatd/shared/locales/client";
import CuratdLogo from "@curatd/ui/components/curatd-logo";
import { LandingHero } from "./landing-hero";

export function HeroSection() {
  const t = useI18n();

  return (
    <LandingHero
      title={t("landing.hero.title")}
      subtitle={t("landing.hero.subtitle")}
      ctaText={t("landing.hero.cta")}
      backgroundVideoSrc="https://zfsumsiiysouykyx.public.blob.vercel-storage.com/hero-home_compressed.mp4"
      logo={<CuratdLogo size="extra-large" color="white" />}
    />
  );
}
