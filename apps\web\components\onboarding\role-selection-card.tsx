"use client";

import { motion } from "motion/react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Building2 } from "lucide-react";
import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
} from "@curatd/ui/components/card";
import { Button } from "@curatd/ui/components/button";
import { cn } from "@curatd/ui/lib/utils";

export type UserRole = "regular_user" | "coach" | "facility_manager";

interface RoleSelectionCardProps {
  role: UserRole;
  title: string;
  description: string;
  features: string[];
  isSelected: boolean;
  onSelect: (role: UserRole) => void;
  selectText: string;
  selectedText: string;
}

const roleIcons = {
  regular_user: Users,
  coach: <PERSON><PERSON><PERSON>,
  facility_manager: Building2,
};

const roleColors = {
  regular_user: "bg-blue-500/10 text-blue-600 border-blue-200",
  coach: "bg-green-500/10 text-green-600 border-green-200",
  facility_manager: "bg-purple-500/10 text-purple-600 border-purple-200",
};

export function RoleSelectionCard({
  role,
  title,
  description,
  features,
  isSelected,
  onSelect,
  selectText,
  selectedText,
}: RoleSelectionCardProps) {
  const Icon = roleIcons[role];

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -4 }}
      className="h-full"
    >
      <Card
        className={cn(
          "relative h-full cursor-pointer transition-all duration-300 hover:shadow-lg",
          isSelected
            ? "ring-2 ring-primary shadow-lg border-primary/50"
            : "hover:border-primary/30"
        )}
        onClick={() => onSelect(role)}
      >
        {/* Selection indicator */}
        {isSelected && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full p-1.5 shadow-lg"
          >
            <Check className="h-4 w-4" />
          </motion.div>
        )}

        <CardHeader className="text-center pb-4">
          {/* Icon with background */}
          <div
            className={cn(
              "mx-auto mb-4 p-4 rounded-2xl border-2 w-fit transition-colors",
              isSelected
                ? "bg-primary/10 text-primary border-primary/30"
                : roleColors[role]
            )}
          >
            <Icon className="h-8 w-8" />
          </div>

          <CardTitle className="text-xl font-bold">{title}</CardTitle>

          <CardDescription className="text-center">
            {description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Features list */}
          <div className="space-y-3">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center gap-3 text-sm"
              >
                <div className="flex-shrink-0 w-1.5 h-1.5 bg-primary rounded-full" />
                <span className="text-muted-foreground">{feature}</span>
              </motion.div>
            ))}
          </div>

          {/* Selection button */}
          <Button
            variant={isSelected ? "default" : "outline"}
            className={cn(
              "w-full transition-all duration-200",
              isSelected && "bg-primary hover:bg-primary/90"
            )}
            onClick={(e) => {
              e.stopPropagation();
              onSelect(role);
            }}
          >
            {isSelected ? (
              <>
                <Check className="h-4 w-4 mr-2" />
                {selectedText}
              </>
            ) : (
              selectText
            )}
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
}
