"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import {
  IconChevronLeft,
  IconChevronRight,
  IconChevronsLeft,
  IconChevronsRight,
  IconDotsVertical,
  IconShield,
  IconTrash,
  IconUserPlus,
} from "@tabler/icons-react";
import { toast } from "sonner";
import { useI18n } from "@curatd/shared/locales/client";

import { Badge } from "@curatd/ui/components/badge";
import { Button } from "@curatd/ui/components/button";
import { Checkbox } from "@curatd/ui/components/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@curatd/ui/components/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@curatd/ui/components/table";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@curatd/ui/components/avatar";

import { useQuery, useMutation } from "convex/react";
import { Enums } from "@curatd/backend";
import { api } from "@curatd/backend/api";
import { DTOs } from "@curatd/backend";

import { ManageRolesDialog } from "./manage-roles-dialog";
import { BulkManageRolesDialog } from "./bulk-manage-roles-dialog";
import { StripeStatusBadge } from "./stripe-status-badge";

interface UserDataTableProps {
  searchTerm?: string;
  roleFilter?: Enums.Roles[];
}

export function UserDataTable({ searchTerm, roleFilter }: UserDataTableProps) {
  const t = useI18n();
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [isManageRolesDialogOpen, setIsManageRolesDialogOpen] =
    React.useState(false);
  const [selectedUser, setSelectedUser] =
    React.useState<DTOs.UserResponseDTO | null>(null);
  const [isBulkManageRolesDialogOpen, setIsBulkManageRolesDialogOpen] =
    React.useState(false);

  // Use the user management hooks
  const usersData = useQuery(
    api.functions.useCases.admin.userManagement.users.list,
    {
      search: searchTerm || "",
      role: roleFilter || [],
      limit: pagination.pageSize,
      offset: pagination.pageIndex * pagination.pageSize,
    }
  );

  const updateUser = useMutation(
    api.functions.useCases.admin.userManagement.users.toggleActive
  );
  const bulkUpdateUsers = useMutation(
    api.functions.useCases.admin.userManagement.users.bulkToggleActive
  );

  const users = usersData?.data || [];
  const totalCount = usersData?.total || 0;

  const columns: ColumnDef<DTOs.UserResponseDTO>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <div className="flex items-center justify-center">
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center justify-center">
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "email",
      header: t("admin.userManagement.user"),
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={user.fallbackAvatarUrl || undefined} />
              <AvatarFallback>
                {user.name
                  ? user.name.charAt(0).toUpperCase()
                  : user.email.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium">{user.name || user.email}</span>
              {user.name && (
                <span className="text-sm text-muted-foreground">
                  {user.email}
                </span>
              )}
            </div>
          </div>
        );
      },
      enableHiding: false,
    },
    {
      accessorKey: "roles",
      header: t("admin.userManagement.roles"),
      cell: ({ row }) => {
        const user = row.original;
        const userRoles = row.original.roles || [];
        return (
          <div className="flex flex-wrap gap-1">
            {userRoles.length > 0 ? (
              userRoles.map((role, index) => (
                <Badge
                  key={`${index}-${role || index}`}
                  variant="secondary"
                  className="text-xs"
                >
                  {role || "Unknown Role"}
                </Badge>
              ))
            ) : (
              <span className="text-sm text-muted-foreground">
                {t("admin.userManagement.noRoles")}
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "stripe_status",
      header: t("admin.userManagement.stripeStatus"),
      cell: ({ row }) => {
        const { coach, customer, facilityManager, managedFacilities } =
          row.original;
        const statuses = [];

        const hasCoachRole = coach !== undefined;
        const hasFacilityManagerRole = facilityManager !== undefined;

        if (hasCoachRole) {
          statuses.push({
            role: t("admin.userManagement.profileTypes.coach"),
            status: coach?.stripeConnectId || "not_connected",
          });
        }

        if (hasFacilityManagerRole) {
          // Collect all statuses from managedFacilities
          const facilityStatuses = (managedFacilities || []).map(
            (facility) => facility.stripeConnectStatus || "not_connected"
          );
          statuses.push({
            role: t("admin.userManagement.profileTypes.facilityManager"),
            status: facilityStatuses, // Pass array of statuses
          });
        }

        if (statuses.length === 0) {
          return <span className="text-sm text-muted-foreground">—</span>;
        }

        return (
          <div className="flex flex-col gap-1">
            {statuses.map((s) => (
              <StripeStatusBadge key={s.role} role={s.role} status={s.status} />
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: t("admin.userManagement.created"),
      cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        if (!createdAt)
          return <span className="text-sm text-muted-foreground">—</span>;
        const date = new Date(createdAt);
        return (
          <span className="text-sm text-muted-foreground">
            {date.toLocaleDateString()}
          </span>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const user = row.original;

        const handleManageRolesClick = (user: DTOs.UserResponseDTO) => {
          setSelectedUser(user);
          setIsManageRolesDialogOpen(true);
        };

        const handleToggleStatus = async () => {
          if (user.isActive) {
            const confirmationMessage = t("admin.userManagement.confirmDelete");
            if (!confirm(confirmationMessage)) {
              return;
            }
          }

          try {
            await updateUser({
              id: user._id,
            });
            toast.success(
              user.isActive
                ? t("admin.userManagement.userDeactivated")
                : t("admin.userManagement.userActivated")
            );
          } catch {
            toast.error(t("admin.userManagement.failedToUpdateStatus"));
          }
        };

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="data-[state=open]:bg-muted text-muted-foreground flex size-8"
                size="icon"
              >
                <IconDotsVertical />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => handleManageRolesClick(user)}>
                <IconShield className="mr-2 h-4 w-4" />
                {t("admin.userManagement.manageRoles")}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleToggleStatus}
                className={
                  user.isActive
                    ? "text-destructive focus:text-destructive"
                    : "text-success focus:text-success"
                }
              >
                {user.isActive ? (
                  <>
                    <IconTrash className="text-destructive mr-2 h-4 w-4" />
                    {t("admin.userManagement.delete")}
                  </>
                ) : (
                  <>
                    <IconUserPlus className="text-success mr-2 h-4 w-4" />
                    {t("admin.userManagement.activate")}
                  </>
                )}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data: users,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    pageCount: Math.ceil(totalCount / pagination.pageSize),
    manualPagination: true,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  const handleBulkDelete = async () => {
    const selectedCount = table.getSelectedRowModel().rows.length;
    const confirmationMessage =
      selectedCount > 1
        ? t("admin.userManagement.bulkActions.confirmDeactivation_plural", {
            count: selectedCount,
          })
        : t("admin.userManagement.bulkActions.confirmDeactivation", {
            count: selectedCount,
          });

    if (!confirm(confirmationMessage)) {
      return;
    }

    try {
      const selectedIds = table
        .getSelectedRowModel()
        .rows.map((row) => row.original._id);
      await bulkUpdateUsers({
        ids: selectedIds,
      });
      toast.success(
        selectedIds.length > 1
          ? t("admin.userManagement.bulkActions.deactivationSuccess_plural", {
              count: selectedIds.length,
            })
          : t("admin.userManagement.bulkActions.deactivationSuccess", {
              count: selectedIds.length,
            })
      );
      table.resetRowSelection();
    } catch {
      toast.error(t("admin.userManagement.bulkActions.deactivationError"));
    }
  };

  // Loading state: if usersData is undefined, show loading
  if (!usersData) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <span className="text-muted-foreground">
            {t("admin.userManagement.loadingUsers")}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-4">
      {/* Bulk Actions */}
      {(table.getIsSomeRowsSelected() || table.getIsAllPageRowsSelected()) && (
        <div className="bg-muted text-muted-foreground flex h-16 items-center justify-between rounded-lg border px-4">
          <div className="text-sm font-medium">
            {table.getSelectedRowModel().rows.length > 1
              ? t("admin.userManagement.bulkActions.selected_plural", {
                  count: table.getSelectedRowModel().rows.length,
                  total: table.getPreFilteredRowModel().rows.length,
                })
              : t("admin.userManagement.bulkActions.selected", {
                  count: table.getSelectedRowModel().rows.length,
                  total: table.getPreFilteredRowModel().rows.length,
                })}
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsBulkManageRolesDialogOpen(true)}
            >
              {t("admin.userManagement.bulkActions.editRoles")}
            </Button>
            <Button variant="destructive" size="sm" onClick={handleBulkDelete}>
              {t("admin.userManagement.bulkActions.delete")}
            </Button>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-hidden rounded-lg border">
        <Table>
          <TableHeader className="bg-muted sticky top-0 z-10">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {users.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <span className="text-muted-foreground">
                    {t("admin.userManagement.noUsersFound")}
                  </span>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">
            {t("admin.userManagement.rowsPerPage")}
          </p>
          <select
            value={`${table.getState().pagination.pageSize}`}
            onChange={(e) => {
              table.setPageSize(Number(e.target.value));
            }}
            className="h-8 w-[70px] rounded border border-input bg-background px-2 text-sm"
          >
            {[10, 20, 30, 40, 50].map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                {pageSize}
              </option>
            ))}
          </select>
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            {t("admin.userManagement.page")}{" "}
            {table.getState().pagination.pageIndex + 1}{" "}
            {t("admin.userManagement.of")} {table.getPageCount()}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">
                {t("admin.userManagement.goToFirstPage")}
              </span>
              <IconChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">
                {t("admin.userManagement.goToPreviousPage")}
              </span>
              <IconChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">
                {t("admin.userManagement.goToNextPage")}
              </span>
              <IconChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">
                {t("admin.userManagement.goToLastPage")}
              </span>
              <IconChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      <ManageRolesDialog
        user={selectedUser}
        open={isManageRolesDialogOpen}
        onOpenChange={setIsManageRolesDialogOpen}
        onSuccess={() => {}}
      />
      <BulkManageRolesDialog
        userIds={table
          .getSelectedRowModel()
          .rows.map((row) => row.original._id)}
        open={isBulkManageRolesDialogOpen}
        onOpenChange={setIsBulkManageRolesDialogOpen}
        onSuccess={() => {
          table.resetRowSelection();
        }}
      />
    </div>
  );
}
