"use client";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@curatd/ui/components/dropdown-menu";
import { Globe } from "lucide-react";
import { Button } from "@curatd/ui/components/button";
import { cn } from "../lib/utils";
import {
  useCurrentLocale,
  useChangeLocale,
} from "@curatd/shared/locales/client";

interface LanguageSwitcherProps {
  size?: "sm" | "lg";
}

export default function LanguageSwitcher({ size = "sm" }: LanguageSwitcherProps) {
  const languages = [
    { code: "en", name: "English" },
    { code: "fr", name: "Français" },
    { code: "pt", name: "Português" },
  ] as const;

  const currentLocale = useCurrentLocale();
  const changeLocale = useChangeLocale();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={size}
          className="flex items-center px-0"
        >
          <Globe className={cn("h-4 w-4 transition-transform")} />
          <span className="uppercase text-xs font-medium hidden md:block">{currentLocale}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[120px]">
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => changeLocale(language.code)}
            className={cn(
              "flex items-center gap-2 transition-colors duration-75",
              currentLocale === language.code && "bg-accent"
            )}
          >
            <span>{language.name}</span>
            {currentLocale === language.code && (
              <span className="ml-auto text-accent-foreground">✓</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
