import { NextFetchEvent, NextRequest, NextResponse } from "next/server"
import { i18n, webAuth } from "@curatd/shared/middleware"
import { middlewareHandler } from "@curatd/shared/middleware"

const middlewares = [
    i18n,
    webAuth,
]

export function middleware(request: NextRequest, event: NextFetchEvent, response: NextResponse) {
    return middlewareHandler(middlewares)(request, event, response)
}

export const config = {
    matcher: ['/((?!api|static|.*\\..*|_next|favicon.ico|robots.txt).*)']
}