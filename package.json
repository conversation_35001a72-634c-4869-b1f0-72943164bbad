{"name": "curatd", "private": true, "scripts": {"build": "npx -y dotenv-cli -e .env.local -- turbo run build", "dev": "npx -y dotenv-cli -e .env.local -- turbo run dev", "lint": "npx -y dotenv-cli -e .env.local -- turbo run lint", "format": "npx -y dotenv-cli -e .env.local -- prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "npx -y dotenv-cli -e .env.local -- turbo run check-types"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.5.4", "typescript": "5.8.2"}, "packageManager": "pnpm@10.12.4", "engines": {"node": ">=20"}, "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "core-js-pure", "esbuild", "sharp", "unrs-resolver"], "overrides": {"@types/react": "19.1.0", "@types/react-dom": "19.1.0"}}}