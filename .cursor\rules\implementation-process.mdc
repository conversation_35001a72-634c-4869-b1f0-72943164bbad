---
description: 
globs: 
alwaysApply: true
---
Input fields
- repo_context: summary of the current repository state and structure
- task_description: precise objective to implement

Output fields
- plan_markdown: path to the generated markdown plan
- implementation_commits: list of commit hashes produced while executing the plan
- verification_report: final pass / fail statement with supporting details

Process

1 Knowledge gathering  
  1.1 Parse the repository tree, dependencies, tests, and build scripts.  
  1.2 Extract all directly relevant files, functions, classes, and data models.  
  1.3 Map each extracted element to task_description, producing a focused context capsule.

2 Plan outlining  
  2.1 Create plan_markdown with sections: Objective, Scope, Affected Files, Implementation Steps, Rollback Strategy.  
  2.2 Store plan_markdown at the repository root and stage it for commit.

3 Focused implementation  
  3.1 Modify only files listed under Affected Files.  
  3.2 Preserve external APIs, tests, and untouched modules.  
  3.3 Commit atomic changes with concise messages referencing plan step numbers.

4 Self-review  
  4.1 Re-read every changed line and plan_markdown without relying on cached context.  
  4.2 Confirm each alteration satisfies its corresponding plan step.  
  4.3 Remove or revert any edit not justified by the plan.

5 Verification  
  5.1 Run full test suite plus static analysis and linters.  
  5.2 Scan git diff for extraneous changes outside Affected Files.  
  5.3 If any check fails, mark verification_report as fail and continue to step 6.

6 Loop control  
  6.1 If verification_report is fail, refine plan_markdown or code, then repeat steps 3-5.  
  6.2 If verification_report is pass, output commit list and mark task complete.


