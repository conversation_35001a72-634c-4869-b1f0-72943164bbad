import { <PERSON><PERSON> } from "@curatd/ui/components/button";
import Link from "next/link";
import { Home, ArrowLeft } from "lucide-react";

export const metadata = {
    title: "Page Not Found - 404",
    description: "The page you're looking for doesn't exist. Return to Curatd to continue your fitness journey.",
    robots: {
        index: false,
        follow: false,
    },
};

export default function NotFoundPage() {
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Page Not Found",
        "description": "The page you're looking for doesn't exist.",
        "url": "https://curatd.com/404",
        "mainEntity": {
            "@type": "Thing",
            "name": "404 Error",
            "description": "Page not found error"
        }
    };

    return (
        <>
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(structuredData),
                }}
            />
            <div className="min-h-screen bg-background text-foreground">
                {/* Simple Navigation */}
                <nav className="fixed top-4 left-0 right-0 z-50 max-w-8xl mx-auto px-8">
                    <div className="relative transition-all duration-75 rounded-full bg-card/80 backdrop-blur-md shadow-sm">
                        <div className="px-4 sm:px-6 md:px-8">
                            <div className="flex justify-between items-center h-16">
                                <Link href="/" className="text-xl font-bold text-foreground">
                                    curatd.
                                </Link>
                                <div className="flex items-center gap-4">
                                    <Link href="/" className="text-foreground hover:text-foreground/60 px-3 py-2 rounded-md text-base font-bold transition-colors">
                                        Home
                                    </Link>
                                    <Link href="/coach" className="text-foreground hover:text-foreground/60 px-3 py-2 rounded-md text-base font-bold transition-colors">
                                        For Coaches
                                    </Link>
                                    <Link href="/gym" className="text-foreground hover:text-foreground/60 px-3 py-2 rounded-md text-base font-bold transition-colors">
                                        For Gyms
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>

                {/* Hero Section */}
                <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
                    {/* Background Video */}
                    <div className="absolute inset-0 z-0 overflow-hidden">
                        <div className="relative w-full h-full">
                            <video
                                className="w-full h-full object-cover"
                                src="https://zfsumsiiysouykyx.public.blob.vercel-storage.com/hero-home_compressed.mp4"
                                autoPlay
                                muted
                                loop
                                playsInline
                                aria-label="Background video showcasing fitness activities"
                                preload="metadata"
                            >
                                <track kind="captions" src="https://zfsumsiiysouykyx.public.blob.vercel-storage.com/hero-home_compressed.mp4" srcLang="en" label="English" />
                                Your browser does not support the video tag.
                            </video>
                            <div className="absolute inset-0 bg-black/40"></div>
                        </div>
                    </div>

                    {/* Content */}
                    <div className="relative z-10 text-center space-y-8 px-6">
                        <div className="text-6xl sm:text-8xl lg:text-9xl font-bold text-primary-foreground/20 mb-8">
                            404
                        </div>
                        <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-primary-foreground text-shadow-[0px_8px_8px_rgb(0_0_0_/_0.15)]">
                            Oops! Page Not Found
                        </h1>
                        <p className="text-primary-foreground text-xl lg:text-2xl text-shadow-[0px_8px_8px_rgb(0_0_0_/_0.15)] max-w-3xl mx-auto">
                            The page you are looking for seems to have wandered off on its own fitness journey.
                            Let&apos;s get you back on track!
                        </p>
                        <div className="flex flex-col sm:flex-row gap-6 justify-center pt-10">
                            <Button
                                asChild
                                size="lg"
                                className="bg-primary-foreground text-secondary px-12 py-6 text-xl rounded-full font-semibold hover:bg-primary-foreground/80"
                            >
                                <Link href="/">
                                    <Home className="w-6 h-6 mr-3" />
                                    Go Home
                                </Link>
                            </Button>
                            <Button
                                asChild
                                size="lg"
                                variant="outline"
                                className="bg-primary-foreground text-primary hover:bg-primary hover:text-primary-foreground px-12 py-6 text-xl rounded-full font-semibold"
                            >
                                <Link href="javascript:history.back()">
                                    <ArrowLeft className="w-6 h-6 mr-3" />
                                    Go Back
                                </Link>
                            </Button>
                        </div>
                    </div>
                </section>

                {/* Additional helpful links section */}
                <section className="py-16 bg-muted/50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                        <h2 className="text-2xl font-bold mb-8 text-foreground">
                            Looking for something specific?
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="p-6 rounded-lg bg-card border border-border hover:border-primary/20 transition-all">
                                <h3 className="text-lg font-semibold mb-3">For Athletes</h3>
                                <p className="text-muted-foreground mb-4">
                                    Book personal training sessions
                                </p>
                                <Button asChild variant="outline" className="rounded-full">
                                    <Link href="/">Start Training</Link>
                                </Button>
                            </div>
                            <div className="p-6 rounded-lg bg-card border border-border hover:border-primary/20 transition-all">
                                <h3 className="text-lg font-semibold mb-3">For Coaches</h3>
                                <p className="text-muted-foreground mb-4">
                                    Join and grow your business
                                </p>
                                <Button asChild variant="outline" className="rounded-full">
                                    <Link href="/coach">Become a Coach</Link>
                                </Button>
                            </div>
                            <div className="p-6 rounded-lg bg-card border border-border hover:border-primary/20 transition-all">
                                <h3 className="text-lg font-semibold mb-3">For Gyms</h3>
                                <p className="text-muted-foreground mb-4">
                                    Partner with us
                                </p>
                                <Button asChild variant="outline" className="rounded-full">
                                    <Link href="/gym">Partner Now</Link>
                                </Button>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Simple Footer */}
                <footer className="bg-background border-t">
                    <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                            <div className="col-span-1 md:col-span-2">
                                <Link href="/" className="text-2xl font-bold text-foreground">
                                    curatd.
                                </Link>
                                <p className="mt-2 text-muted-foreground">
                                    Private wellness sessions anywhere, anytime.
                                </p>
                            </div>
                            <div>
                                <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-4">
                                    Quick Links
                                </h3>
                                <ul className="space-y-2">
                                    <li><Link href="/" className="text-muted-foreground hover:text-foreground">Home</Link></li>
                                    <li><Link href="/coach" className="text-muted-foreground hover:text-foreground">For Coaches</Link></li>
                                    <li><Link href="/gym" className="text-muted-foreground hover:text-foreground">For Gyms</Link></li>
                                    <li><Link href="/contact" className="text-muted-foreground hover:text-foreground">Contact</Link></li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider mb-4">
                                    Legal
                                </h3>
                                <ul className="space-y-2">
                                    <li><Link href="/privacy" className="text-muted-foreground hover:text-foreground">Privacy Policy</Link></li>
                                    <li><Link href="/terms" className="text-muted-foreground hover:text-foreground">Terms of Service</Link></li>
                                </ul>
                            </div>
                        </div>
                        <div className="mt-8 pt-8 border-t border-border">
                            <p className="text-center text-muted-foreground">
                                © 2024 Curatd. All rights reserved.
                            </p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
} 