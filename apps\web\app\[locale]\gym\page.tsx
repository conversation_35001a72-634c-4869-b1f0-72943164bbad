import { GymHeroSection } from "@/components/landing/gym/gym-hero-section";
import { GymBenefitsSection } from "@/components/landing/gym/gym-benefits-section";
import { GymHowItWorksSection } from "@/components/landing/gym/gym-how-it-works-section";
import { GymCallToActionSection } from "@/components/landing/gym/gym-call-to-action-section";
import { Navbar } from "@/components/landing/navbar";
import { Footer } from "@/components/landing/footer";

export default function GymPage() {
    const gymStructuredData = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Curatd for Gyms",
        "description": "Partner with Curatd to unlock new revenue and maximize usage of your fitness facility",
        "url": "https://curatd.com/gym",
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://curatd.com"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "For Gyms",
                    "item": "https://curatd.com/gym"
                }
            ]
        },
        "mainEntity": {
            "@type": "Service",
            "name": "Gym Partnership Program",
            "description": "Partner with Curatd to welcome premium clients and top-tier coaches to your facility",
            "provider": {
                "@type": "Organization",
                "name": "Curatd",
                "@id": "https://curatd.com/#organization"
            },
            "serviceType": "Partnership",
            "audience": {
                "@type": "Audience",
                "audienceType": "Fitness Facilities"
            },
            "offers": {
                "@type": "Offer",
                "description": "No contracts or long-term commitments",
                "priceSpecification": {
                    "@type": "PriceSpecification",
                    "price": "0",
                    "priceCurrency": "USD"
                }
            }
        }
    };

    return (
        <>
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify(gymStructuredData),
                }}
            />
            <main className="min-h-screen">
                <Navbar />
                <GymHeroSection />
                <GymHowItWorksSection />
                <GymBenefitsSection />
                <GymCallToActionSection />
                <Footer />
            </main>
        </>
    );
}

export const metadata = {
    title: "Curatd for Gyms – Grow Your Fitness Business",
    description: "Curatd helps gyms connect with new customers, manage memberships, and offer exclusive deals. Join the fitness revolution!",
    alternates: {
        canonical: "https://curatd.com/gym",
    },
    openGraph: {
        title: "Curatd for Gyms – Grow Your Fitness Business",
        description: "Curatd helps gyms connect with new customers, manage memberships, and offer exclusive deals. Join the fitness revolution!",
        type: "website",
        url: "https://curatd.com/gym",
        images: [
            {
                url: "/og-image.jpg",
                width: 1200,
                height: 630,
                alt: "Curatd for Gyms",
            },
        ],
    },
    twitter: {
        card: "summary_large_image",
        title: "Curatd for Gyms – Grow Your Fitness Business",
        description: "Curatd helps gyms connect with new customers, manage memberships, and offer exclusive deals. Join the fitness revolution!",
        images: ["/og-image.jpg"],
    },
}; 