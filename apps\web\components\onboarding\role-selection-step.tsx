"use client";

import { useState } from "react";
import { motion } from "motion/react";
import { useI18n } from "@curatd/shared/locales/client";
import { Button } from "@curatd/ui/components/button";
import { RoleSelectionCard, type UserRole } from "./role-selection-card";

interface RoleSelectionStepProps {
  onRoleSelect: (role: UserRole) => void;
  onContinue: () => void;
  selectedRole?: UserRole;
}

export function RoleSelectionStep({
  onRoleSelect,
  onContinue,
  selectedRole,
}: RoleSelectionStepProps) {
  const t = useI18n();
  const [localSelectedRole, setLocalSelectedRole] = useState<
    UserRole | undefined
  >(selectedRole);

  const handleRoleSelect = (role: UserRole) => {
    setLocalSelectedRole(role);
    onRoleSelect(role);
  };

  const roles: Array<{
    role: UserRole;
    title: string;
    description: string;
    features: string[];
  }> = [
    {
      role: "regular_user",
      title: t("onboarding.roleSelection.regularUser.title"),
      description: t("onboarding.roleSelection.regularUser.description"),
      features: [
        t("onboarding.roleSelection.regularUser.features.bookWorkouts"),
        t(
          "onboarding.roleSelection.regularUser.features.findCertifiedTrainers"
        ),
        t("onboarding.roleSelection.regularUser.features.trackProgress"),
        t("onboarding.roleSelection.regularUser.features.joinGroupSessions"),
      ],
    },
    {
      role: "coach",
      title: t("onboarding.roleSelection.coach.title"),
      description: t("onboarding.roleSelection.coach.description"),
      features: [
        t("onboarding.roleSelection.coach.features.createTrainingPrograms"),
        t("onboarding.roleSelection.coach.features.manageSchedule"),
        t("onboarding.roleSelection.coach.features.acceptPayments"),
        t("onboarding.roleSelection.coach.features.buildClientBase"),
      ],
    },
    {
      role: "facility_manager",
      title: t("onboarding.roleSelection.facilityManager.title"),
      description: t("onboarding.roleSelection.facilityManager.description"),
      features: [
        t("onboarding.roleSelection.facilityManager.features.listFacility"),
        t("onboarding.roleSelection.facilityManager.features.manageBookings"),
        t(
          "onboarding.roleSelection.facilityManager.features.partnerWithTrainers"
        ),
        t("onboarding.roleSelection.facilityManager.features.earnRevenue"),
      ],
    },
  ];

  return (
    <div className="w-full max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-4"
      >
        <h2 className="text-3xl md:text-4xl font-bold">
          {t("onboarding.roleSelection.title")}
        </h2>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          {t("onboarding.roleSelection.subtitle")}
        </p>
      </motion.div>

      {/* Role Cards */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="grid md:grid-cols-3 gap-6 lg:gap-8"
      >
        {roles.map((roleData) => (
          <RoleSelectionCard
            key={roleData.role}
            role={roleData.role}
            title={roleData.title}
            description={roleData.description}
            features={roleData.features}
            isSelected={localSelectedRole === roleData.role}
            onSelect={handleRoleSelect}
            selectText={t("onboarding.roleSelection.buttons.selectRole")}
            selectedText={t("onboarding.roleSelection.buttons.selected")}
          />
        ))}
      </motion.div>

      {/* Continue Button */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="flex justify-center pt-8"
      >
        <Button
          size="lg"
          onClick={onContinue}
          disabled={!localSelectedRole}
          className="px-12 py-3 text-lg rounded-full font-semibold min-w-[200px]"
        >
          {t("onboarding.roleSelection.buttons.continue")}
        </Button>
      </motion.div>

      {/* Skip option */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="text-center"
      >
      </motion.div>
    </div>
  );
}
