import { Tabs, TabList, TabTrigger, TabSlot } from "expo-router/ui";
import { Text } from "react-native";
import React from "react";

export default function TabLayout() {
  return (
    <Tabs>
      <TabSlot />
      <TabList
        style={{
          flexDirection: "row",
          justifyContent: "space-around",
          alignItems: "center",
          borderTopWidth: 1,
          borderTopColor: "#ccc",
          backgroundColor: "white",
          paddingBottom: 20,
          paddingTop: 10,
        }}
      >
        <TabTrigger name="index" href="/">
          <Text>Home</Text>
        </TabTrigger>
        <TabTrigger name="settings" href="/settings">
          <Text>Settings</Text>
        </TabTrigger>
      </TabList>
    </Tabs>
  );
}
