import { validateConvexEnv, validateStripeEnv, validateResendEnv, type ConvexEnv, type StripeEnv, type ResendEnv, AppUrlsEnv, validateAppUrlsEnv } from './env';

/**
 * Get validated Convex configuration for CLIENT-SIDE usage
 * Only validates public environment variables that are safe for client
 */
export function getConvexClientConfig() {
    const env = {
        CONVEX_URL: process.env.NEXT_PUBLIC_CONVEX_URL || process.env.EXPO_PUBLIC_CONVEX_URL,
    };

    if (!env.CONVEX_URL) {
        throw new Error('CONVEX_URL is required for client-side usage. Set NEXT_PUBLIC_CONVEX_URL or EXPO_PUBLIC_CONVEX_URL');
    }

    return {
        CONVEX_URL: env.CONVEX_URL,
    };
}

/**
 * Get validated Convex configuration for SERVER-SIDE usage
 * Validates all environment variables including service role key
 * Throws an error if required environment variables are missing or invalid
 */
export function getConvexConfig(): ConvexEnv {
    const env = {
        CONVEX_URL: process.env.CONVEX_URL || process.env.NEXT_PUBLIC_CONVEX_URL,
    };

    return validateConvexEnv(env);
}

/**
 * Get validated Stripe configuration
 * Throws an error if required environment variables are missing or invalid
 */
export function getStripeConfig(): StripeEnv {
    const env = {
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
        STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
        STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
        STRIPE_CLIENT_ID: process.env.STRIPE_CLIENT_ID,
        STRIPE_SESSION_REDIRECT_URL: process.env.STRIPE_SESSION_REDIRECT_URL,
        CHECKOUT_SUCCESS_REDIRECT_URL: process.env.CHECKOUT_SUCCESS_REDIRECT_URL,
        CHECKOUT_CANCEL_REDIRECT_URL: process.env.CHECKOUT_CANCEL_REDIRECT_URL,
    };

    return validateStripeEnv(env);
}

/**
 * Get validated Resend configuration
 * Throws an error if required environment variables are missing or invalid
 */
export function getResendConfig(): ResendEnv {
    const env = {
        RESEND_API_KEY: process.env.RESEND_API_KEY,
    };

    return validateResendEnv(env);
}

export function getAppUrlsConfig(): AppUrlsEnv {
    const env = {
        NEXT_PUBLIC_WEB_APP_URL: process.env.NEXT_PUBLIC_WEB_APP_URL,
        NEXT_PUBLIC_ADMIN_APP_URL: process.env.NEXT_PUBLIC_ADMIN_APP_URL,
    };

    return validateAppUrlsEnv(env);
}

/**
 * Validate environment variables on startup (SERVER-SIDE ONLY)
 * Call this in your app's startup code to ensure all required env vars are present
 */
export function validateEnvironment() {
    try {
        getConvexConfig();
        console.log('✅ Convex environment validation passed');
    } catch (error) {
        console.error('❌ Convex environment validation failed:', error);
        throw error;
    }

    try {
        getStripeConfig();
        console.log('✅ Stripe environment validation passed');
    } catch (error) {
        console.error('❌ Stripe environment validation failed:', error);
        throw error;
    }

    try {
        getResendConfig();
        console.log('✅ Resend environment validation passed');
    } catch (error) {
        console.error('❌ Resend environment validation failed:', error);
        throw error;
    }

    try {
        getAppUrlsConfig();
        console.log('✅ App URLs environment validation passed');
    } catch (error) {
        console.error('❌ App URLs environment validation failed:', error);
        throw error;
    }
} 