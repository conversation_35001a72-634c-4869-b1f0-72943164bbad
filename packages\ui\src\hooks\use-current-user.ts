import { useI18n } from '@curatd/shared/locales/client'
import { useEffect, useState } from 'react'

export default function useCurrentUser() {
    const t = useI18n()
    const [profile, setProfile] = useState<{
        id?: string
        name?: string
        email?: string
        avatar?: string
    }>({
        id: undefined,
        name: undefined,
        email: undefined,
        avatar: undefined,
    })

    useEffect(() => {
        const fetchProfileName = async () => {
            // TODO: Implement get user with Convex instead of Supabase
            // const { data, error } = await createClient().auth.getUser()
            // if (error) {
            //     console.error(error)
            // }

            //  setProfile({
            //         id: data.user?.id,
            //         name: data.user?.user_metadata.full_name || data.user?.email?.split('@')[0] || t('user.default_name'),
            //         email: data.user?.email ?? t('user.default_name'),
            //         avatar: data.user?.user_metadata.avatar_url ?? '',
            //     })
            // }

        }
        fetchProfileName()
    }, [])

    return profile;
}
