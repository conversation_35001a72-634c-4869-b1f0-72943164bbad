"use client";

import { Button } from "@curatd/ui/components/button";
import { motion } from "motion/react";
import Link from "next/link";

export default function PrivacyPage() {
  const sectionVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  const fadeInVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        when: "beforeChildren" as const,
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <motion.div
      className="min-h-screen bg-background"
      initial="hidden"
      animate="visible"
      variants={fadeInVariants}
    >
      {/* Header */}
      <motion.div
        className="bg-primary text-background py-16"
        variants={sectionVariants}
      >
        <div className="max-w-4xl mx-auto px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Privacy Policy
          </h1>
          <p className="text-background/80 text-lg">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>
      </motion.div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="space-y-12">
          {/* Introduction */}
          <motion.section variants={sectionVariants}>
            <h2 className="text-2xl font-bold text-foreground mb-4">
              Introduction
            </h2>
            <p className="text-muted-foreground leading-relaxed">
              Welcome to Curatd. We respect your privacy and are committed to
              protecting your personal data. This privacy policy will inform you
              about how we look after your personal data when you visit our
              website and tell you about your privacy rights and how the law
              protects you.
            </p>
          </motion.section>

          {/* Data Collection */}
          <motion.section variants={sectionVariants}>
            <h2 className="text-2xl font-bold text-foreground mb-4">
              What Data We Collect
            </h2>
            <p className="text-muted-foreground leading-relaxed mb-4">
              We may collect, use, store and transfer different kinds of
              personal data about you which we have grouped together as follows:
            </p>
            <ul className="space-y-2 text-muted-foreground">
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Identity Data: includes first name, maiden name, last name,
                  username or similar identifier, marital status, title, date of
                  birth and gender.
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Contact Data: includes billing address, delivery address,
                  email address and telephone numbers.
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Technical Data: includes internet protocol (IP) address, your
                  login data, browser type and version, time zone setting and
                  location, browser plug-in types and versions, operating system
                  and platform.
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Profile Data: includes your username and password, purchases
                  or orders made by you, your interests, preferences, feedback
                  and survey responses.
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Usage Data: includes information about how you use our
                  website, products and services.
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Marketing and Communications Data: includes your preferences
                  in receiving marketing from us and our third parties and your
                  communication preferences.
                </span>
              </li>
            </ul>
          </motion.section>

          {/* Data Use */}
          <motion.section variants={sectionVariants}>
            <h2 className="text-2xl font-bold text-foreground mb-4">
              How We Use Your Data
            </h2>
            <p className="text-muted-foreground leading-relaxed mb-4">
              We will only use your personal data when the law allows us to.
              Most commonly, we will use your personal data in the following
              circumstances:
            </p>
            <ul className="space-y-2 text-muted-foreground">
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  To provide and maintain our service
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  To notify you about changes to our service
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  To allow you to participate in interactive features of our
                  service when you choose to do so
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  To provide customer care and support
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  To provide analysis or valuable information so that we can
                  improve the service
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  To monitor the usage of the service
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  To detect, prevent and address technical issues
                </span>
              </li>
            </ul>
          </motion.section>

          {/* Data Sharing */}
          <motion.section variants={sectionVariants}>
            <h2 className="text-2xl font-bold text-foreground mb-4">
              Data Sharing
            </h2>
            <p className="text-muted-foreground leading-relaxed mb-4">
              We do not sell, trade, or otherwise transfer your personal data to
              third parties without your consent, except as described in this
              policy. We may share your information with:
            </p>
            <ul className="space-y-2 text-muted-foreground">
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Service providers who assist us in operating our website and
                  conducting our business
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Professional advisers including lawyers, bankers, auditors and
                  insurers
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Government bodies that require us to report processing
                  activities
                </span>
              </li>
            </ul>
          </motion.section>

          {/* Data Security */}
          <motion.section variants={sectionVariants}>
            <h2 className="text-2xl font-bold text-foreground mb-4">
              Data Security
            </h2>
            <p className="text-muted-foreground leading-relaxed">
              We have put in place appropriate security measures to prevent your
              personal data from being accidentally lost, used or accessed in an
              unauthorised way, altered or disclosed. We limit access to your
              personal data to those employees, agents, contractors and other
              third parties who have a business need to know.
            </p>
          </motion.section>

          {/* Your Rights */}
          <motion.section variants={sectionVariants}>
            <h2 className="text-2xl font-bold text-foreground mb-4">
              Your Rights
            </h2>
            <p className="text-muted-foreground leading-relaxed mb-4">
              Under certain circumstances, you have rights under data protection
              laws in relation to your personal data:
            </p>
            <ul className="space-y-2 text-muted-foreground">
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Request access to your personal data
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Request correction of your personal data
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Request erasure of your personal data
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Object to processing of your personal data
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Request restriction of processing your personal data
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Request transfer of your personal data
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-primary mr-2 mt-2 w-2 h-2 rounded-full bg-primary flex-shrink-0"></span>
                <span className="leading-relaxed">
                  Right to withdraw consent
                </span>
              </li>
            </ul>
          </motion.section>

          {/* Contact */}
          <motion.section variants={sectionVariants}>
            <div className="bg-muted/50 rounded-lg p-8">
              <h2 className="text-2xl font-bold text-foreground mb-4">
                Contact Us
              </h2>
              <p className="text-muted-foreground leading-relaxed">
                If you have any questions about this Privacy Policy, please
                contact us by <NAME_EMAIL> or through our contact
                form on the website.
              </p>
            </div>
          </motion.section>
        </div>

        {/* Back to Home */}
        <motion.div className="mt-16 text-center" variants={sectionVariants}>
          <Link href="/">
            <Button variant="outline">← Back to Home</Button>
          </Link>
        </motion.div>
      </div>
    </motion.div>
  );
}
