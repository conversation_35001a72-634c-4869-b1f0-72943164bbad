import { z } from "zod";

// =====================================================
// ENUMS & LITERALS
// =====================================================

export const ProfileType = z.enum(["coach", "facility_manager"]);
export type ProfileType = z.infer<typeof ProfileType>;

// =====================================================
// INPUT DTOs
// =====================================================

export const CreateAccountLinkSchema = z.object({
    userId: z.string().uuid("Invalid user ID"),
    profileType: ProfileType,
    returnUrl: z.string().url("Invalid return URL"),
    refreshUrl: z.string().url("Invalid refresh URL"),
});

// =====================================================
// OUTPUT DTOs
// =====================================================

export const AccountLinkResponseSchema = z.object({
    url: z.string().url(),
    created: z.number(),
    expires_at: z.number(),
});

// =====================================================
// TYPE EXPORTS
// =====================================================

export type CreateAccountLinkInput = z.infer<typeof CreateAccountLinkSchema>;
export type AccountLinkResponse = z.infer<typeof AccountLinkResponseSchema>;

// =====================================================
// ERROR CLASSES
// =====================================================

export abstract class StripeConnectError extends Error {
    abstract readonly code: string;
    abstract readonly statusCode: number;

    constructor(message: string, public readonly details?: unknown) {
        super(message);
        this.name = this.constructor.name;
    }
}

export class StripeValidationError extends StripeConnectError {
    readonly code = "VALIDATION_ERROR";
    readonly statusCode = 400;

    constructor(
        message: string,
        public readonly fieldErrors?: Record<string, string[]>
    ) {
        super(message, fieldErrors);
    }
}

export class StripeAuthorizationError extends StripeConnectError {
    readonly code = "AUTHORIZATION_ERROR";
    readonly statusCode = 403;

    constructor(operation: string) {
        super(`Insufficient permissions to perform operation: ${operation}`);
    }
}

export class StripeOperationError extends StripeConnectError {
    readonly code = "STRIPE_API_ERROR";
    readonly statusCode = 500;

    constructor(message: string, public readonly originalError?: unknown) {
        super(`Stripe API operation failed: ${message}`, originalError);
    }
}

// =====================================================
// METHOD CONTRACTS
// =====================================================

/**
 * @file
 * This use case defines the core operations for interacting with Stripe Connect.
 * Unlike the `user-management` use case, which is designed for direct client-side
 * access via PostgREST, this module requires a server-side implementation.
 *
 * The primary reason for this architectural difference is security. Operations
 * like creating Stripe accounts and generating onboarding links require the
 * `STRIPE_SECRET_KEY`, which must never be exposed on the client. Therefore,
 * the functions in this use case must be called from a secure backend environment,
 * such as a Next.js API route or a server action, which can then safely interact
 * with the Stripe API.
 */
export interface StripeConnectCore {
    /**
     * Creates a Stripe Express account and generates an onboarding link.
     * @param input - User ID, profile type, and redirect URLs.
     * @returns Promise resolving to the account link details.
     */
    createAccountLink(
        input: CreateAccountLinkInput
    ): Promise<AccountLinkResponse>;
} 