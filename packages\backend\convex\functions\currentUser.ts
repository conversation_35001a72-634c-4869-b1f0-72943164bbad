import { query } from "../../convex/_generated/server";
import { getCurrentUser } from "../../helpers/auth/getCurrentUser";
import { Nullable } from "../../helpers/nullableObjectReturnValidator";
import { doc } from "convex-helpers/validators";
import schema from "../schema";
import { v } from "convex/values";


export const get = query({
    args: {},
    returns: Nullable(v.object(doc(schema, "users").fields)),
    handler: async (ctx) => {
        return await getCurrentUser(ctx);
    },
});