"use client";

import { useI18n } from "@curatd/shared/locales/client";
import { Badge } from "@curatd/ui/components/badge";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ipContent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@curatd/ui/components/tooltip";

type BadgeVariant = "default" | "secondary" | "destructive" | "outline";

const getStripeBadgeVariant = (
  status: string | null | undefined
): BadgeVariant => {
  switch (status) {
    case "active":
      return "default";
    case "onboarding":
      return "secondary";
    case "restricted":
      return "destructive";
    case "inactive":
    case "not_connected":
    default:
      return "outline";
  }
};

interface StripeStatusBadgeProps {
  role: string;
  status: string | string[] | null | undefined;
}

export function StripeStatusBadge({ role, status }: StripeStatusBadgeProps) {
  const t = useI18n();

  // If status is an array, summarize
  if (Array.isArray(status)) {
    // Count occurrences of each status
    const counts: Record<string, number> = {};
    for (const s of status) {
      const key = (s || "not_connected").toLowerCase();
      counts[key] = (counts[key] || 0) + 1;
    }
    // Build summary string
    const summary = Object.entries(counts)
      .map(([key, count]) => {
        let label = "";
        switch (key) {
          case "active":
            label = t("admin.userManagement.stripeStatuses.active");
            break;
          case "onboarding":
            label = t("admin.userManagement.stripeStatuses.onboarding");
            break;
          case "inactive":
            label = t("admin.userManagement.stripeStatuses.inactive");
            break;
          case "restricted":
            label = t("admin.userManagement.stripeStatuses.restricted");
            break;
          default:
            label = t("admin.userManagement.stripeStatuses.not_connected");
            break;
        }
        return `${count} ${label}`;
      })
      .join(", ");

    // Pick the most severe status for badge color and label
    const priority = [
      "active",
      "onboarding",
      "restricted",
      "inactive",
      "not_connected",
    ];
    let badgeStatus = "not_connected";
    for (const p of priority) {
      if (counts[p]) {
        badgeStatus = p;
        break;
      }
    }
    let badgeLabel = "";
    switch (badgeStatus) {
      case "active":
        badgeLabel = t("admin.userManagement.stripeStatuses.active");
        break;
      case "onboarding":
        badgeLabel = t("admin.userManagement.stripeStatuses.onboarding");
        break;
      case "inactive":
        badgeLabel = t("admin.userManagement.stripeStatuses.inactive");
        break;
      case "restricted":
        badgeLabel = t("admin.userManagement.stripeStatuses.restricted");
        break;
      default:
        badgeLabel = t("admin.userManagement.stripeStatuses.not_connected");
        break;
    }

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm whitespace-nowrap">
                {role}:
              </span>
              <Badge variant={getStripeBadgeVariant(badgeStatus)}>
                {badgeLabel}
              </Badge>
            </div>
          </TooltipTrigger>
          <TooltipContent>{summary}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // If status is a string, behave as before
  let statusText: string;
  switch ((status || "not_connected").toLowerCase()) {
    case "active":
      statusText = t("admin.userManagement.stripeStatuses.active");
      break;
    case "onboarding":
      statusText = t("admin.userManagement.stripeStatuses.onboarding");
      break;
    case "inactive":
      statusText = t("admin.userManagement.stripeStatuses.inactive");
      break;
    case "restricted":
      statusText = t("admin.userManagement.stripeStatuses.restricted");
      break;
    default:
      statusText = t("admin.userManagement.stripeStatuses.not_connected");
      break;
  }

  return (
    <div className="flex items-center gap-2">
      <span className="font-medium text-sm whitespace-nowrap">{role}:</span>
      <Badge variant={getStripeBadgeVariant(status)}>{statusText}</Badge>
    </div>
  );
}
