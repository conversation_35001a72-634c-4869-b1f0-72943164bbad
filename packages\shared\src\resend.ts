import { Resend } from 'resend';
import { getResendConfig } from './config';

/**
 * Create a configured Resend client instance
 * Throws an error if RESEND_API_KEY is not configured
 */
export function createResendClient(): Resend {
    const { RESEND_API_KEY } = getResendConfig();
    return new Resend(RESEND_API_KEY);
}

/**
 * Get a singleton Resend client instance
 * Reuses the same instance across calls for better performance
 */
let resendClient: Resend | null = null;

export function getResendClient(): Resend {
    if (!resendClient) {
        resendClient = createResendClient();
    }
    return resendClient;
}

/**
 * Email sending options based on Resend API
 */
export interface SendEmailOptions {
    /** Sender email address. To include a friendly name, use the format "Your Name <<EMAIL>>" */
    from: string;
    /** Recipient email address(es). For multiple addresses, send as an array of strings. Max 50. */
    to: string | string[];
    /** Email subject */
    subject: string;
    /** The HTML version of the message */
    html?: string;
    /** The plain text version of the message */
    text?: string;
    /** The React component used to write the message (Node.js only) */
    react?: React.ReactNode;
    /** Bcc recipient email address(es) */
    bcc?: string | string[];
    /** Cc recipient email address(es) */
    cc?: string | string[];
    /** Reply-to email address(es) */
    reply_to?: string | string[];
    /** Schedule email to be sent later */
    scheduled_at?: string;
    /** Custom headers to add to the email */
    headers?: Record<string, string>;
    /** Filename and content of attachments (max 40MB per email) */
    attachments?: Array<{
        /** Content of an attached file, passed as a buffer or Base64 string */
        content?: Buffer | string;
        /** Name of attached file */
        filename?: string;
        /** Path where the attachment file is hosted */
        path?: string;
        /** Content type for the attachment */
        content_type?: string;
    }>;
    /** Custom data passed in key/value pairs */
    tags?: Array<{
        /** The name of the email tag */
        name: string;
        /** The value of the email tag */
        value: string;
    }>;
}

/**
 * Response from Resend email send API
 */
export interface SendEmailResponse {
    /** Unique identifier for the sent email */
    id: string;
}

/**
 * Send an email using Resend
 * 
 * @param options Email sending options
 * @param idempotencyKey Optional idempotency key to prevent duplicate emails
 * @returns Promise with email ID
 * 
 * @example
 * ```typescript
 * const result = await sendEmail({
 *   from: 'Your App <<EMAIL>>',
 *   to: '<EMAIL>',
 *   subject: 'Welcome!',
 *   html: '<h1>Welcome to our app!</h1>',
 *   text: 'Welcome to our app!'
 * });
 * console.log('Email sent with ID:', result.id);
 * ```
 */
export async function sendEmail(
    options: SendEmailOptions,
    idempotencyKey?: string
): Promise<SendEmailResponse> {
    const resend = getResendClient();

    const emailData: any = {
        from: options.from,
        to: options.to,
        subject: options.subject,
    };

    // Add optional fields if provided
    if (options.html) emailData.html = options.html;
    if (options.text) emailData.text = options.text;
    if (options.react) emailData.react = options.react;
    if (options.bcc) emailData.bcc = options.bcc;
    if (options.cc) emailData.cc = options.cc;
    if (options.reply_to) emailData.reply_to = options.reply_to;
    if (options.scheduled_at) emailData.scheduled_at = options.scheduled_at;
    if (options.headers) emailData.headers = options.headers;
    if (options.attachments) emailData.attachments = options.attachments;
    if (options.tags) emailData.tags = options.tags;

    // Add idempotency key if provided
    const requestOptions: any = {};
    if (idempotencyKey) {
        requestOptions.headers = {
            'Idempotency-Key': idempotencyKey,
        };
    }

    const { data, error } = await resend.emails.send(emailData, requestOptions);

    if (error) {
        throw error;
    }

    if (!data) {
        throw new Error('sendEmail returned no data and no error');
    }

    return { id: data.id };
}

/**
 * Send a batch of emails using Resend
 * 
 * @param emails Array of email options
 * @returns Promise with array of email results
 */
export async function sendBatchEmails(emails: SendEmailOptions[]): Promise<SendEmailResponse[]> {
    const resend = getResendClient();

    const emailsData = emails.map(options => {
        const emailData: any = {
            from: options.from,
            to: options.to,
            subject: options.subject,
        };

        if (options.html) emailData.html = options.html;
        if (options.text) emailData.text = options.text;
        if (options.react) emailData.react = options.react;
        if (options.bcc) emailData.bcc = options.bcc;
        if (options.cc) emailData.cc = options.cc;
        if (options.reply_to) emailData.reply_to = options.reply_to;
        if (options.scheduled_at) emailData.scheduled_at = options.scheduled_at;
        if (options.headers) emailData.headers = options.headers;
        if (options.attachments) emailData.attachments = options.attachments;
        if (options.tags) emailData.tags = options.tags;

        return emailData;
    });

    const { data, error } = await resend.batch.send(emailsData);
    if (error) {
        throw error;
    }

    if (!data) {
        throw new Error('sendBatchEmails returned no data and no error');
    }

    return data.data;
}

/**
 * Common email templates and utilities
 */
export const EmailTemplates = {
    /**
     * Create a simple text + HTML email
     */
    createSimpleEmail(options: {
        from: string;
        to: string | string[];
        subject: string;
        content: string;
        isHtml?: boolean;
    }): SendEmailOptions {
        const emailOptions: SendEmailOptions = {
            from: options.from,
            to: options.to,
            subject: options.subject,
        };

        if (options.isHtml) {
            emailOptions.html = options.content;
            // Generate plain text version by stripping HTML tags
            emailOptions.text = options.content.replace(/<[^>]*>/g, '');
        } else {
            emailOptions.text = options.content;
        }

        return emailOptions;
    },

    /**
     * Create a welcome email template
     */
    createWelcomeEmail(options: {
        from: string;
        to: string;
        userName: string;
        appName: string;
        loginUrl?: string;
    }): SendEmailOptions {
        const { from, to, userName, appName, loginUrl } = options;

        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h1 style="color: #333;">Welcome to ${appName}!</h1>
                <p>Hi ${userName},</p>
                <p>Welcome to ${appName}! We're excited to have you on board.</p>
                ${loginUrl ? `<p><a href="${loginUrl}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Get Started</a></p>` : ''}
                <p>If you have any questions, feel free to reach out to our support team.</p>
                <p>Best regards,<br>The ${appName} Team</p>
            </div>
        `;

        const text = `
            Welcome to ${appName}!
            
            Hi ${userName},
            
            Welcome to ${appName}! We're excited to have you on board.
            ${loginUrl ? `Get started: ${loginUrl}` : ''}
            
            If you have any questions, feel free to reach out to our support team.
            
            Best regards,
            The ${appName} Team
        `;

        return {
            from,
            to,
            subject: `Welcome to ${appName}!`,
            html,
            text,
        };
    },

    /**
     * Create a password reset email template
     */
    createPasswordResetEmail(options: {
        from: string;
        to: string;
        userName: string;
        resetUrl: string;
        appName: string;
        expiryHours?: number;
    }): SendEmailOptions {
        const { from, to, userName, resetUrl, appName, expiryHours = 24 } = options;

        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h1 style="color: #333;">Password Reset Request</h1>
                <p>Hi ${userName},</p>
                <p>You recently requested to reset your password for your ${appName} account.</p>
                <p><a href="${resetUrl}" style="background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
                <p>This link will expire in ${expiryHours} hours.</p>
                <p>If you didn't request this, please ignore this email.</p>
                <p>Best regards,<br>The ${appName} Team</p>
            </div>
        `;

        const text = `
            Password Reset Request
            
            Hi ${userName},
            
            You recently requested to reset your password for your ${appName} account.
            
            Reset your password: ${resetUrl}
            
            This link will expire in ${expiryHours} hours.
            
            If you didn't request this, please ignore this email.
            
            Best regards,
            The ${appName} Team
        `;

        return {
            from,
            to,
            subject: `Password Reset - ${appName}`,
            html,
            text,
        };
    },
};

/**
 * Utility to validate email addresses
 */
export function validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Utility to normalize email address (lowercase, trim)
 */
export function normalizeEmail(email: string): string {
    return email.trim().toLowerCase();
} 