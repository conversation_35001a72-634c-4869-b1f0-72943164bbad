import { NextResponse } from "next/server";
import { stripeConnectServer } from "@curatd/shared/backend/use-cases/stripe-connect/server";
import {
    CreateAccountLinkSchema,
    StripeConnectError,
} from "@curatd/shared/backend/use-cases/stripe-connect/core";

export async function POST(request: Request) {

    try {
        // 1. Authenticate the user with convex instead of supabase
        // const { data: { user }, error: authError } = await supabase.auth.getUser();
        // if (authError || !user) {
        //     return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        // }

        // const body = await request.json();
        // const { profileType } = body;

        // 2. Construct the input for the use case
        // const input = {
        //     userId: user.id,
        //     profileType,
        //     // Construct absolute URLs for Stripe redirects
        //     returnUrl: `${process.env.NEXT_PUBLIC_WEB_APP_URL}/app/business?stripe_return=true`,
        //     refreshUrl: `${process.env.NEXT_PUBLIC_WEB_APP_URL}/app/business?stripe_refresh=true`,
        // };

        // 3. Validate the input
        // const validatedInput = CreateAccountLinkSchema.parse(input);

        // 4. Call the shared use case
        // const accountLink = await stripeConnectServer.createAccountLink(validatedInput);

        // 5. Return the onboarding URL to the client
        // return NextResponse.json(accountLink);
        return NextResponse.json({ error: "Not implemented" }, { status: 501 });

    } catch (error) {
        console.error("[STRIPE_ACCOUNT_LINK_POST]", error);

        if (error instanceof StripeConnectError) {
            return NextResponse.json(
                { error: error.message, code: error.code },
                { status: error.statusCode }
            );
        }

        if (error instanceof Error && 'issues' in error) { // Zod validation error
            return NextResponse.json(
                { error: "Invalid input", issues: error.issues },
                { status: 400 }
            );
        }

        return new NextResponse("Internal Server Error", { status: 500 });
    }
} 