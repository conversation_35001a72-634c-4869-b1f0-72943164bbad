"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { useI18n } from "@curatd/shared/locales/client";
import {
  IconUser,
  IconBuilding,
  IconSwitchHorizontal,
} from "@tabler/icons-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@curatd/ui/components/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@curatd/ui/components/sidebar";
import {
  BusinessEntityInfo,
  BusinessEntity,
} from "@/hooks/use-business-navigation";
import { Skeleton } from "@curatd/ui/components/skeleton";

interface EntitySwitcherProps {
  availableEntities: BusinessEntityInfo[];
  activeEntityInfo: BusinessEntityInfo | null;
  switchEntity: (entity: BusinessEntity) => void;
  hasMultipleEntities: boolean;
}

function EntitySwitcherSkeleton() {
  return (
    <div className="flex items-center gap-2 w-full">
      <Skeleton className="h-8 w-8 rounded-full" />
      <div className="flex flex-col gap-1 flex-1">
        <Skeleton className="h-4 w-[80%]" />
        <Skeleton className="h-3 w-[60%]" />
      </div>
      <div className="flex flex-col h-full items-center justify-center gap-0.5 mr-4">
        <Skeleton className="size-1 rounded-full" />
        <Skeleton className="size-1 rounded-full" />
        <Skeleton className="size-1 rounded-full" />
      </div>
    </div>
  );
}

export function EntitySwitcher({
  availableEntities,
  activeEntityInfo,
  switchEntity,
  hasMultipleEntities,
}: EntitySwitcherProps) {
  const t = useI18n();
  const router = useRouter();
  const { isMobile } = useSidebar();

  if (!availableEntities || !activeEntityInfo) {
    return <EntitySwitcherSkeleton />;
  }

  if (!hasMultipleEntities || !activeEntityInfo) {
    return null;
  }

  const handleEntitySwitch = (entity: BusinessEntityInfo) => {
    switchEntity(entity.type);
    // Navigate to the new entity's dashboard
    const entityPath = entity.type === "coach" ? "coach" : "facility-manager";
    router.push(`/app/business/${entityPath}`);
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <IconSwitchHorizontal className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {activeEntityInfo.displayName}
                </span>
                <span className="truncate text-xs text-muted-foreground">
                  {t("business.nav.switchEntity")}
                </span>
              </div>
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-xs font-normal text-muted-foreground">
              {t("business.nav.switchEntity")}
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {availableEntities.map((entity) => (
              <DropdownMenuItem
                key={entity.type}
                onClick={() => handleEntitySwitch(entity)}
                className="flex items-center gap-3"
              >
                {entity.type === "coach" ? (
                  <IconUser className="size-4" />
                ) : (
                  <IconBuilding className="size-4" />
                )}
                <span className="flex-1">{entity.displayName}</span>
                {entity.type === activeEntityInfo.type && (
                  <div className="size-2 rounded-full bg-primary" />
                )}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
