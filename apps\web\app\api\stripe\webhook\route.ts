import { getStripeConfig } from "@curatd/shared/config";
import { NextResponse, type NextRequest } from "next/server";
import Stripe from "stripe";

// Initialize Stripe client
const stripe = new Stripe(getStripeConfig().STRIPE_SECRET_KEY as string);
const webhookSecret = getStripeConfig().STRIPE_WEBHOOK_SECRET as string;

// Disable Next.js body parser for this route to access the raw body
export const config = {
    api: {
        bodyParser: false,
    },
};

/**
 * Handles incoming Stripe Connect webhooks.
 *
 * @param req - The incoming Next.js request object.
 * @returns A response object.
 */
export async function POST(req: NextRequest) {
    const sig = req.headers.get("stripe-signature");
    if (!sig) {
        return NextResponse.json(
            { error: "Missing Stripe signature" },
            { status: 400 }
        );
    }

    const buf = await req.arrayBuffer();
    const rawBody = Buffer.from(buf);

    let event: Stripe.Event;

    try {
        event = stripe.webhooks.constructEvent(rawBody, sig, webhookSecret);
    } catch (err) {
        const message = err instanceof Error ? err.message : "Unknown error";
        console.error(`❌ Stripe webhook signature error: ${message}`);
        return NextResponse.json(
            { error: `Webhook Error: ${message}` },
            { status: 400 }
        );
    }

    // Handle the event
    switch (event.type) {
        case "account.updated":
            await handleAccountUpdated(event.data.object as Stripe.Account);
            break;
        case "account.application.authorized": {
            const authorizedAccount = await stripe.accounts.retrieve(
                event.account as string
            );
            await handleAccountUpdated(authorizedAccount);
            break;
        }
        // Add other event types to handle here
        default:
            console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
}

/**
 * Processes the 'account.updated' event from Stripe.
 *
 * @param account - The Stripe account object from the event payload.
 */
async function handleAccountUpdated(account: Stripe.Account) {
    const accountId = account.id;

    // Determine the account status based on Stripe's data
    const status = getStatusFromStripeAccount(account);

    // Update both coach and facility manager profiles, as we don't know the profile type from the webhook.
    // The update will only affect the table that has a matching stripe_account_id.
    // TODO: Implement update coach and facility manager profiles with Convex instead of Supabase
    // const { error: coachError } = await supabase
    //     .from("coach_profiles")
    //     .update({ stripe_account_status: status })
    //     .eq("stripe_account_id", accountId);

    // if (coachError) {
    //     console.error(`Error updating coach profile for Stripe account ${accountId}:`, coachError.message);
    // }

    // const { error: facilityManagerError } = await supabase
    //     .from("facility_manager_profiles")
    //     .update({ stripe_account_status: status })
    //     .eq("stripe_account_id", accountId);

    // if (facilityManagerError) {
    //     console.error(`Error updating facility manager profile for Stripe account ${accountId}:`, facilityManagerError.message);
    // }

    // console.log(`✅ Processed account event for ${accountId}. Status set to: ${status}`);
}

/**
 * Determines a simplified status from a Stripe Account object.
 *
 * @param account - The Stripe account object.
 * @returns A simplified status string that matches the database enum.
 */
function getStatusFromStripeAccount(account: Stripe.Account): string {
    const disabledReason = account.requirements?.disabled_reason;
    if (disabledReason) {
        switch (disabledReason) {
            case 'rejected.fraud':
            case 'rejected.incomplete_verification':
            case 'rejected.listed':
            case 'rejected.other':
            case 'rejected.platform_fraud':
            case 'rejected.platform_other':
            case 'rejected.platform_terms_of_service':
            case 'rejected.terms_of_service':
                return 'rejected';

            case 'requirements.past_due':
            case 'requirements.pending_verification':
            case 'listed':
            case 'platform_paused':
            case 'action_required.requested_capabilities':
            case 'under_review':
            case 'other':
                return 'restricted';
        }
    }

    if (account.requirements?.pending_verification && (account.requirements.pending_verification.length > 0)) {
        return 'in_review';
    }
    if (account.requirements?.eventually_due && (account.requirements.eventually_due.length > 0)) {
        return "restricted_soon";
    }
    if (account.requirements?.currently_due && (account.requirements.currently_due.length > 0)) {
        return "onboarding";
    }
    if (account.details_submitted && account.charges_enabled && account.payouts_enabled) {
        return "active";
    }
    if (!account.payouts_enabled || !account.charges_enabled) {
        return 'inactive';
    }

    return "inactive";
} 