import { redirect } from "next/navigation";
import { getCurrentUser } from "@curatd/shared/backend/use-cases/user-management/server";
import { CoachDashboardClient } from "@/components/business/coach-dashboard-client";

export default async function CoachDashboardPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const user = await getCurrentUser();
  const params = await searchParams;

  // Handle the redirect from Stripe Connect onboarding
  if (params?.stripe_return === "true") {
    console.log("stripe_return");
    return redirect("/app/business/coach?onboarded=true");
  }

  // For refresh URLs, redirect to the dashboard with an error
  if (params?.stripe_refresh === "true") {
    return redirect("/app/business/coach?onboarded=false&error=refresh");
  }

  return (
    <CoachDashboardClient user={user} coachProfile={user.coach_profiles} />
  );
}
