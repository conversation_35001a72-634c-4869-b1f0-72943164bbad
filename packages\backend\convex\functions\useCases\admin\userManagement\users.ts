import { mutation, MutationCtx, QueryCtx, query } from "../../../../../convex/_generated/server";
import { withRole } from "../../../../../wrappers/auth/withRole";
import { loadUserRelationships } from "../../../../../helpers/auth/userRelationships";
import { Doc } from "../../../../../convex/_generated/dataModel";
import { Roles } from "../../../../../types/enums";
import { assignRolesToUser } from "../../../../../helpers/admin/users/assignRolesToUser";
import { revokeRolesFromUser } from "../../../../../helpers/admin/users/revokeRolesFromUser";
import {
    UsersListRequestDTOObjectValidator,
    UsersListResponseDTO,
    UsersGetRequestDTOObjectValidator,
    UsersGetResponseDTOObjectValidator,
    UsersToggleActiveRequestDTOObjectValidator,
    UsersToggleActiveResponseDTOObjectValidator,
    UsersBulkToggleActiveRequestDTOObjectValidator,
    UsersBulkToggleActiveResponseDTOObjectValidator,
    UsersAssignRolesRequestDTOObjectValidator,
    UsersAssignRolesResponseDTOObjectValidator,
    UsersRevokeRolesRequestDTOObjectValidator,
    UsersRevokeRolesResponseDTOObjectValidator,
    UsersBulkAssignRolesRequestDTOObjectValidator,
    UsersBulkAssignRolesResponseDTOObjectValidator,
    UsersBulkRevokeRolesRequestDTOObjectValidator,
    UsersBulkRevokeRolesResponseDTOObjectValidator,
    UsersListRequestDTO,
    UsersGetRequestDTO,
    UsersToggleActiveRequestDTO,
    UsersToggleActiveResponseDTO,
    UsersBulkToggleActiveRequestDTO,
    UsersBulkToggleActiveResponseDTO,
    UsersAssignRolesRequestDTO,
    UsersAssignRolesResponseDTO,
    UsersRevokeRolesRequestDTO,
    UsersRevokeRolesResponseDTO,
    UsersBulkAssignRolesRequestDTO,
    UsersBulkAssignRolesResponseDTO,
    UsersBulkRevokeRolesRequestDTO,
    UsersBulkRevokeRolesResponseDTO,
    UsersGetResponseDTO,
    UsersListResponseDTOValidator,
} from "../../../../../types/dtos";


export const list = query({
    args: UsersListRequestDTOObjectValidator,
    returns: UsersListResponseDTOValidator,
    handler: withRole(Roles.ADMIN, async (ctx: QueryCtx, args: UsersListRequestDTO): Promise<UsersListResponseDTO> => {
        // Use search index if search is provided, which also supports role filtering
        let response: Doc<"users">[] = [];
        if (args.search) {
            const users = await ctx.db
                .query("users")
                .withSearchIndex("search_email", q => {
                    let query = q.search("email", args.search);
                    // Apply role filter if roles are specified
                    if (args.role && args.role.length > 0) {
                        query = query.eq("roles", args.role);
                    }
                    return query;
                })
                .take(args.limit + (args.offset || 0));

            response = args.offset ? users.slice(args.offset) : users;
        } else if (args.role && args.role.length > 0) {
            // Use regular index for role filtering when no search is provided
            const users = await ctx.db
                .query("users")
                .withIndex("by_roles", q =>
                    q.eq("roles", args.role)
                )
                .order("desc")
                .take(args.limit + (args.offset || 0));

            response = args.offset ? users.slice(args.offset) : users;
        } else {
            // No filters - use full table scan
            const users = await ctx.db
                .query("users")
                .order("desc")
                .take(args.limit + (args.offset || 0));

            response = args.offset ? users.slice(args.offset) : users;
        }

        // TODO: use ShardedCounter instead of this extra expensive query
        const total = (await ctx.db.query("users").collect()).length;
        const usersWithRelationships = await Promise.all(response.map(async (user) => {
            const { hashedPassword, providerId, ...userWithoutSensitiveInfo } = user;
            return {
                ...userWithoutSensitiveInfo,
                ...(await loadUserRelationships(ctx, user)),
            };
        }));

        return {
            data: usersWithRelationships,
            total,
        };
    }),
});

export const get = query({
    args: UsersGetRequestDTOObjectValidator,
    returns: UsersGetResponseDTOObjectValidator,
    handler: withRole(Roles.ADMIN, async (ctx: QueryCtx, args: UsersGetRequestDTO): Promise<UsersGetResponseDTO> => {
        const user = await ctx.db.get<"users">(args.id);
        if (!user) {
            throw new Error("User not found");
        }
        const relationships = await loadUserRelationships(ctx, user);
        // We don't want to return the hashed password or providerId so we manually omit them
        const { hashedPassword, providerId, ...userWithoutSensitiveInfo } = user;
        return {
            ...userWithoutSensitiveInfo,
            ...relationships,
        };
    }),
});

export const toggleActive = mutation({
    args: UsersToggleActiveRequestDTOObjectValidator,
    returns: UsersToggleActiveResponseDTOObjectValidator,
    handler: withRole(Roles.ADMIN, async (ctx, args: UsersToggleActiveRequestDTO): Promise<UsersToggleActiveResponseDTO> => {
        const user = await ctx.db.get<"users">(args.id);
        if (!user) {
            throw new Error("User not found");
        }
        await (ctx as MutationCtx).db.patch(args.id, { isActive: !user.isActive });
        return null;
    }),
});

export const bulkToggleActive = mutation({
    args: UsersBulkToggleActiveRequestDTOObjectValidator,
    returns: UsersBulkToggleActiveResponseDTOObjectValidator,
    handler: withRole(Roles.ADMIN, async (ctx, args: UsersBulkToggleActiveRequestDTO): Promise<UsersBulkToggleActiveResponseDTO> => {
        let users = await Promise.all(args.ids.map(async (id) => {
            return await ctx.db.get<"users">(id);
        }));
        users = users.filter(Boolean);
        if (users.length === 0) {
            throw new Error("Users not found");
        }

        // Toggle isActive for each user
        const updates = users.map(user => ({
            id: user!._id,
            isActive: !user!.isActive
        }));

        let updatedCount = 0;
        // Update each user individually since bulk patch doesn't exist
        for (const update of updates) {
            await (ctx as MutationCtx).db.patch(update.id, { isActive: update.isActive });
            updatedCount++;
        }

        return updatedCount;
    }),
});

export const assignRoles = mutation({
    args: UsersAssignRolesRequestDTOObjectValidator,
    returns: UsersAssignRolesResponseDTOObjectValidator,
    handler: withRole(Roles.ADMIN, async (ctx, args: UsersAssignRolesRequestDTO): Promise<UsersAssignRolesResponseDTO> => {
        // Use the helper function to handle role assignment
        const updates = await assignRolesToUser(ctx as MutationCtx, args.user_id, args.roles);

        // If there are updates to apply, patch the user
        if (Object.keys(updates).length > 0) {
            await (ctx as MutationCtx).db.patch(args.user_id, updates);
        }

        return null;
    }),
});

export const revokeRoles = mutation({
    args: UsersRevokeRolesRequestDTOObjectValidator,
    returns: UsersRevokeRolesResponseDTOObjectValidator,
    handler: withRole(Roles.ADMIN, async (ctx, args: UsersRevokeRolesRequestDTO): Promise<UsersRevokeRolesResponseDTO> => {
        // Use the helper function to handle role removal
        const updates = await revokeRolesFromUser(ctx as MutationCtx, args.user_id, args.roles);

        // If there are updates to apply, patch the user
        if (Object.keys(updates).length > 0) {
            await (ctx as MutationCtx).db.patch(args.user_id, updates);
        }

        return null;
    }),
});

export const bulkAssignRoles = mutation({
    args: UsersBulkAssignRolesRequestDTOObjectValidator,
    returns: UsersBulkAssignRolesResponseDTOObjectValidator,
    handler: withRole(Roles.ADMIN, async (ctx, args: UsersBulkAssignRolesRequestDTO): Promise<UsersBulkAssignRolesResponseDTO> => {
        const errors: string[] = [];

        // Validate that all user IDs exist
        const users = await Promise.all(args.user_ids.map(async (id) => {
            const user = await ctx.db.get<"users">(id);
            if (!user) {
                errors.push(`User with ID ${id} not found`);
                return null;
            }
            return user;
        }));

        // Filter out any null users (those that were not found)
        const validUsers = users.filter(Boolean);

        if (validUsers.length === 0) {
            return { success: false, errors };
        }

        // Use the helper function to assign roles to each user
        for (const user of validUsers) {
            try {
                const updates = await assignRolesToUser(ctx as MutationCtx, user!._id, args.roles);

                // If there are updates to apply, patch the user
                if (Object.keys(updates).length > 0) {
                    await (ctx as MutationCtx).db.patch(user!._id, updates);
                }
            } catch (error) {
                errors.push(`Failed to assign roles to user ${user!._id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }

        return { success: errors.length === 0, errors };
    }),
});

export const bulkRevokeRoles = mutation({
    args: UsersBulkRevokeRolesRequestDTOObjectValidator,
    returns: UsersBulkRevokeRolesResponseDTOObjectValidator,
    handler: withRole(Roles.ADMIN, async (ctx, args: UsersBulkRevokeRolesRequestDTO): Promise<UsersBulkRevokeRolesResponseDTO> => {
        const errors: string[] = [];

        // Validate that all user IDs exist
        const users = await Promise.all(args.user_ids.map(async (id) => {
            const user = await ctx.db.get<"users">(id);
            if (!user) {
                errors.push(`User with ID ${id} not found`);
                return null;
            }
            return user;
        }));

        // Filter out any null users (those that were not found)
        const validUsers = users.filter(Boolean);

        if (validUsers.length === 0) {
            return { success: false, errors };
        }

        // Use the helper function to revoke roles from each user
        for (const user of validUsers) {
            try {
                const updates = await revokeRolesFromUser(ctx as MutationCtx, user!._id, args.roles);

                // If there are updates to apply, patch the user
                if (Object.keys(updates).length > 0) {
                    await (ctx as MutationCtx).db.patch(user!._id, updates);
                }
            } catch (error) {
                errors.push(`Failed to revoke roles from user ${user!._id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }

        return { success: errors.length === 0, errors };
    }),
});