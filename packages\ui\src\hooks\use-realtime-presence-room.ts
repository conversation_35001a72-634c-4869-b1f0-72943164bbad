'use client'

import { useCurrentUserImage } from '@curatd/ui/hooks/use-current-user-image'
import { useCurrentUserName } from '@curatd/ui/hooks/use-current-user-name'
import { useEffect, useState } from 'react'


export type RealtimeUser = {
  id: string
  name: string
  image: string
}

export const useRealtimePresenceRoom = (roomName: string) => {
  const currentUserImage = useCurrentUserImage()
  const currentUserName = useCurrentUserName()

  const [users, setUsers] = useState<Record<string, RealtimeUser>>({})

  useEffect(() => {
    // TODO: Implement realtime presence room with Convex instead of Supabase
    // const room = supabase.channel(roomName)

    // room
    //   .on('presence', { event: 'sync' }, () => {
    //     const newState = room.presenceState<{ image: string; name: string }>()

    // const newUsers = Object.fromEntries(
    //   Object.entries(newState).map(([key, values]) => [
    //       key,
    //     values && values[0] ? { name: values[0].name, image: values[0].image } : null,
    //   ]).filter(Boolean)
    //     ) as Record<string, RealtimeUser>
    //     setUsers(newUsers)
    //   })
    //   .subscribe(async (status) => {
    //     if (status !== 'SUBSCRIBED') {
    //       return
    //     }

    //     await room.track({
    //       name: currentUserName,
    //     image: currentUserImage,
    //     })
    // })

    return () => {
      // room.unsubscribe()
    }
  }, [roomName, currentUserName, currentUserImage]);

  return { users };
}
