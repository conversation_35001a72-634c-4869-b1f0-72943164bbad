# LOCAL Environment Variables
# Generated by CURATD CLI

# Convex
CONVEX_DEPLOYMENT=dev:brave-oriole-132 # team: man<PERSON>-<PERSON><PERSON><PERSON><PERSON>, project: curatd
NEXT_PUBLIC_CONVEX_URL=https://brave-oriole-132.convex.cloud
EXPO_PUBLIC_CONVEX_URL=https://brave-oriole-132.convex.cloud
CONVEX_URL=https://brave-oriole-132.convex.cloud

AUTH_GOOGLE_ID=166815177663-3les7t74n4s8a2rfmm3nie4vc88tq2tu.apps.googleusercontent.com
AUTH_GOOGLE_SECRET=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

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51RcxHv4T7OuqC3gNgs45EVHul16JKT35kRpdN2pBxJiRDzgnyKqwDq8VHQGGqU8zJ4xHKhpFvq4ZwBSM2CUhTWlg00TCdw5FtY
STRIPE_WEBHOOK_SECRET=whsec_29f7bb9cc9999ec95c6a5b45a51f742b57051e73ec9ebb5580e0298b578d936f
# STRIPE_CLIENT_ID=your_stripe_connect_client_id_here

# Client-side Stripe (Next.js)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RcxHv4T7OuqC3gNE7uUdLxc76ms062JaBzOayIrEwRoI3qtqZ4uRuTlANv5J2eHfE6NMoVcqjtdDyZoITrRPLe800JcvQClT7

# Client-side Stripe (Expo)
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RcxHv4T7OuqC3gNE7uUdLxc76ms062JaBzOayIrEwRoI3qtqZ4uRuTlANv5J2eHfE6NMoVcqjtdDyZoITrRPLe800JcvQClT7

# Resend Configuration
RESEND_API_KEY=re_P6Tna7JX_NJJQJPJYZAPvZ9Cz8kDt2Zqj

# Project URLs
NEXT_PUBLIC_WEB_APP_URL=http://localhost:3000
NEXT_PUBLIC_ADMIN_APP_URL=http://localhost:3001