import {
    convexAuthNextjsMiddleware,
    createRouteMatcher,
} from "@convex-dev/auth/nextjs/server";
import type { MiddlewareWrapperType } from './types';
import { NextFetchEvent, NextRequest, NextResponse } from 'next/server';

const genericPublicRoutes = [
    "(.*)/auth/(.*)",
]

const isPublicWebRoute = createRouteMatcher(["/", ...genericPublicRoutes])

const isPublicAdminRoute = createRouteMatcher(genericPublicRoutes)

export const adminAuth: MiddlewareWrapperType = (next) => {
    return (request: NextRequest, event: NextFetchEvent, response: NextResponse) => {
        return convexAuthNextjsMiddleware(async (req, { convexAuth }) => {
            if (!isPublicAdminRoute(req) && !(await convexAuth.isAuthenticated())) {
                const url = request.nextUrl.clone();
                url.pathname = "/auth/login";
                return NextResponse.redirect(url)
            }
            // check if user is admin
            return next(req, event, response)
        }, {
            cookieConfig: {
                maxAge: 60 * 60 * 24 * 365 // 1 year for admins
            }
        })(request, event)
    }
}

export const webAuth: MiddlewareWrapperType = (next) => {
    return (request: NextRequest, event: NextFetchEvent, response: NextResponse) => {
        return convexAuthNextjsMiddleware(async (req, { convexAuth }) => {
            if (!isPublicWebRoute(req) && !(await convexAuth.isAuthenticated())) {
                const url = request.nextUrl.clone();
                url.pathname = "/auth/login";
                return NextResponse.redirect(url)
            }
            return next(req, event, response)
        }, {
            cookieConfig: {
                maxAge: 60 * 60 * 24 * 90 // 90 days for web users
            }
        })(request, event)
    }
}



