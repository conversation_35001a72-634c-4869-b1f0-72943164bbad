{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@curatd/shared": "workspace:*", "@curatd/ui": "workspace:*", "@curatd/backend": "workspace:*", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-slot": "^1.1.2", "@tabler/icons-react": "^3.34.0", "lucide-react": "^0.522.0", "motion": "^12.19.2", "next": "15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "sonner": "^2.0.5", "stripe": "^18.2.1", "swr": "^2.2.6"}, "devDependencies": {"@curatd/eslint-config": "workspace:*", "@curatd/typescript-config": "workspace:*", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "eslint": "^9", "eslint-config-next": "15.3.4", "typescript": "^5.7.3"}}