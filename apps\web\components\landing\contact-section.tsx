"use client";

import { useState } from "react";
import { But<PERSON> } from "@curatd/ui/components/button";
import { Input } from "@curatd/ui/components/input";
import { Textarea } from "@curatd/ui/components/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@curatd/ui/components/card";
import { useI18n } from "@curatd/shared/locales/client";
import { Title } from "./title";

export function ContactSection() {
    const t = useI18n();
    const [formData, setFormData] = useState({
        name: "",
        email: "",
        subject: "",
        message: "",
    });
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        // Simulate form submission
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log("Contact form submitted:", formData);

        // Reset form
        setFormData({
            name: "",
            email: "",
            subject: "",
            message: "",
        });

        setIsSubmitting(false);
    };

    const handleInputChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value,
        }));
    };

    return (
        <section id="contact" className="pt-34 pb-16">
            <div className="container mx-auto px-4 md:px-6 lg:px-8">
                <div className="max-w-6xl mx-auto">
                    <div className="text-center mb-16">
                        <Title
                            title={t("landing.contact.title")}
                            subtitle={t("landing.contact.subtitle")}
                            titleClassName="font-bold"
                            size="md"
                        />
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
                        {/* Contact Information */}
                        <div className="space-y-8">
                            <div>
                                <h3 className="text-2xl font-semibold mb-6">
                                    {t("landing.contact.getInTouch")}
                                </h3>
                                <div className="space-y-4">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-5 h-5 rounded-full bg-primary flex-shrink-0"></div>
                                        <span className="text-muted-foreground">
                                            {t("landing.contact.email")}
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-3">
                                        <div className="w-5 h-5 rounded-full bg-primary flex-shrink-0"></div>
                                        <span className="text-muted-foreground">
                                            {t("landing.contact.phone")}
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-3">
                                        <div className="w-5 h-5 rounded-full bg-primary flex-shrink-0"></div>
                                        <span className="text-muted-foreground">
                                            {t("landing.contact.address")}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h4 className="text-lg font-medium mb-4">
                                    {t("landing.contact.followUs")}
                                </h4>
                                <div className="flex space-x-4">
                                    <Button variant="outline" size="sm">
                                        Instagram
                                    </Button>
                                    <Button variant="outline" size="sm">
                                        Twitter
                                    </Button>
                                    <Button variant="outline" size="sm">
                                        LinkedIn
                                    </Button>
                                </div>
                            </div>
                        </div>

                        {/* Contact Form */}
                        <Card className="pt-6">
                            <CardHeader>
                                <CardTitle>{t("landing.contact.sendMessage")}</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Input
                                                name="name"
                                                placeholder={t("landing.contact.namePlaceholder")}
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Input
                                                name="email"
                                                type="email"
                                                placeholder={t("landing.contact.emailPlaceholder")}
                                                value={formData.email}
                                                onChange={handleInputChange}
                                                required
                                            />
                                        </div>
                                    </div>

                                    <Input
                                        name="subject"
                                        placeholder={t("landing.contact.subjectPlaceholder")}
                                        value={formData.subject}
                                        onChange={handleInputChange}
                                        required
                                    />

                                    <Textarea
                                        name="message"
                                        placeholder={t("landing.contact.messagePlaceholder")}
                                        value={formData.message}
                                        onChange={handleInputChange}
                                        rows={5}
                                        required
                                    />

                                    <Button
                                        type="submit"
                                        className="w-full bg-foreground text-background hover:bg-foreground/80"
                                        disabled={isSubmitting}
                                    >
                                        {isSubmitting
                                            ? t("landing.contact.sending")
                                            : t("landing.contact.sendMessage")
                                        }
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </section>
    );
} 