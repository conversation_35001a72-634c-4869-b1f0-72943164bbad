import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  transpilePackages: ["@curatd/ui"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.pexels.com",
      },
      {
        protocol: "https",
        hostname: "videos.pexels.com",
      },
      {
        protocol: "https",
        hostname: "zfsumsiiysouykyx.public.blob.vercel-storage.com",
      },
    ],
  },
};

export default nextConfig;
