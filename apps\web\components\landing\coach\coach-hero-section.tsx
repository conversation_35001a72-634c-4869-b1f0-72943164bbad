"use client";

import { Button } from "@curatd/ui/components/button";
import { useI18n } from "@curatd/shared/locales/client";
import { LandingHero } from "../landing-hero";

export function CoachHeroSection() {
    const t = useI18n();

    const handleCTAClick = () => {
        const signupSection = document.querySelector('#coach-signup');
        if (signupSection) {
            signupSection.scrollIntoView({ behavior: 'smooth' });
        }
    };

    // Split title for color highlight
    const [mainTitle, highlight] = t('landing.coach.hero.title').split('.');

    return (
        <LandingHero
            title={<span>{mainTitle}. {highlight}.</span>}
            subtitle={t('landing.coach.hero.subtitle')}
            backgroundVideoSrc="https://zfsumsiiysouykyx.public.blob.vercel-storage.com/hero-coach.mp4"
            ctaText={t('landing.coach.hero.cta')}
            onCtaClick={handleCTAClick}
            backgroundImageSrc={undefined}
        >
        </LandingHero>
    );
} 