---
title: Authentication & Authorization
layout: ../../layouts/MainLayout.astro
---

# Authentication & Authorization

Curatd implements a robust authentication and authorization system using Convex and custom wrappers.

## Authentication Setup

Authentication is configured in:
- `convex/auth.ts`: Core authentication logic
- `convex/auth.config.ts`: Configuration for auth providers

The system supports multiple authentication methods, including email-based and third-party providers.

### One-Time Password Authentication

For email-based authentication, Curatd uses a custom OTP (One-Time Password) system implemented in `convex/ResendOTP.ts`.

## User Roles

Users in the system can have different roles, defined in `types/enums.ts`:

```typescript
export enum Roles {
    ADMIN = "admin",
    FACILITY_MANAGER = "facility_manager",
    COACH = "coach",
    CUSTOMER = "customer"
}
```

## Role-Based Access Control

The `withRole` wrapper provides role-based access control for Convex functions. This wrapper follows Convex's pattern for function composition:

```typescript
import { query } from "./_generated/server";
import { withRole } from "../../../../../wrappers/auth/withRole";
import { Roles } from "../../../../../types/enums";
import { v } from "convex/values";

// Function that only admins can access
export const adminOnlyFunction = query({
    args: {
        param1: v.string(),
        param2: v.optional(v.number())
    },
    returns: v.object({
        success: v.boolean(),
        data: v.array(v.object({
            id: v.id("users"),
            name: v.string()
        }))
    }),
    handler: withRole(Roles.ADMIN, async (ctx, args) => {
        // Only users with ADMIN role can access this function
        const users = await ctx.db
            .query("users")
            .withIndex("by_active", q => q.eq("active", true))
            .collect();
            
        return {
            success: true,
            data: users.map(user => ({
                id: user._id,
                name: user.name
            }))
        };
    })
});
```

The implementation of `withRole` checks the current user's roles before executing the function:

```typescript
export function withRole<T extends QueryCtx | MutationCtx, R>(role: Roles, func: (ctx: T, ...args: any[]) => Promise<R>) {
    return async (ctx: T, args: any) => {
        const user = await getCurrentUser(ctx);
        if (!user) {
            throw new Error("User not found");
        }
        if (!userHasRole(ctx, role)) {
            throw new Error(`User does not have required role: ${role}`);
        }
        return func(ctx, args);
    }
}
```

## User Functions

Current user information can be accessed using helper functions:

```typescript
import { getCurrentUser } from "../../helpers/auth/getCurrentUser";
import { userHasRole } from "../../helpers/auth/userHasRole";

// Get the current authenticated user
const user = await getCurrentUser(ctx);

// Check if the user has a specific role
const isAdmin = await userHasRole(ctx, Roles.ADMIN);
```

## User Relationships

The system also manages relationships between users and other entities through the `loadUserRelationships` helper:

```typescript
import { loadUserRelationships } from "../helpers/auth/userRelationships";

// Load relationships for a user
const relationships = await loadUserRelationships(ctx, userId);
```

## Invitation System

New users can be invited to the platform with pre-assigned roles using the invitation system:

1. An invitation is created with specific roles attached
2. User receives an invitation via email
3. Upon accepting, the user is created with the specified roles

## Authorization Flow

1. User authenticates with the system
2. Backend verifies credentials and retrieves user information
3. When accessing protected functions, the `withRole` wrapper checks if the user has the required role
4. If authorized, the function executes; otherwise, an error is thrown
