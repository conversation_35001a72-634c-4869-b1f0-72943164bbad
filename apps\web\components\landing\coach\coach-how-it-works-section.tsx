"use client";

import React from "react";
import { useI18n } from "@curatd/shared/locales/client";
import { User<PERSON><PERSON>, User<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { HowItWorksSectionGeneric } from "../how-it-works-section-generic";

export function CoachHowItWorksSection() {
    const t = useI18n();

    const steps = [
        {
            number: "01",
            icon: <UserPlus className="w-8 h-8 text-foreground" />,
            title: t('landing.coach.howItWorks.steps.signUp.title'),
            description: t('landing.coach.howItWorks.steps.signUp.description'),
        },
        {
            number: "02",
            icon: <UserCheck className="w-8 h-8 text-foreground" />,
            title: t('landing.coach.howItWorks.steps.getVerified.title'),
            description: t('landing.coach.howItWorks.steps.getVerified.description'),
        },
        {
            number: "03",
            icon: <Dumbbell className="w-8 h-8 text-foreground" />,
            title: t('landing.coach.howItWorks.steps.startCoaching.title'),
            description: t('landing.coach.howItWorks.steps.startCoaching.description'),
        }
    ];

    return (
        <HowItWorksSectionGeneric
            title={t("landing.coach.howItWorks.title")}
            steps={steps}
            columns={3}
        />
    );
} 