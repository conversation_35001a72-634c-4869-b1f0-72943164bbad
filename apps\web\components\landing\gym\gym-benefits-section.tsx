"use client";

import { useI18n } from "@curatd/shared/locales/client";
import { Users, DollarSign, Megaphone, Settings } from "lucide-react";
import { Title } from "../title";
import { Badge } from "@curatd/ui/components/badge";
import { VideoCarousel } from "../video-carousel";

export function GymBenefitsSection() {
    const t = useI18n();

    const benefits = [
        {
            icon: Users,
            title: t("landing.gym.benefits.items.fillYourGym.title"),
            description: t("landing.gym.benefits.items.fillYourGym.description"),
            subtitle: t("landing.gym.benefits.items.fillYourGym.subtitle"),
            badge: t("landing.gym.benefits.items.fillYourGym.badge"),
        },
        {
            icon: DollarSign,
            title: t("landing.gym.benefits.items.earnEveryTime.title"),
            description: t("landing.gym.benefits.items.earnEveryTime.description"),
            subtitle: t("landing.gym.benefits.items.earnEveryTime.subtitle"),
            badge: t("landing.gym.benefits.items.earnEveryTime.badge"),
        },
        {
            icon: Megaphone,
            title: t("landing.gym.benefits.items.freePromotion.title"),
            description: t("landing.gym.benefits.items.freePromotion.description"),
            subtitle: t("landing.gym.benefits.items.freePromotion.subtitle"),
            badge: t("landing.gym.benefits.items.freePromotion.badge"),
        },
        {
            icon: Settings,
            title: t("landing.gym.benefits.items.simpleFlexible.title"),
            description: t("landing.gym.benefits.items.simpleFlexible.description"),
            subtitle: t("landing.gym.benefits.items.simpleFlexible.subtitle"),
            badge: t("landing.gym.benefits.items.simpleFlexible.badge"),
        },
    ];

    const videoUrl = "https://videos.pexels.com/video-files/12188782/12188782-uhd_2560_1440_25fps.mp4"

    return (
        <section id="gym" className="bg-background py-8 sm:py-12 md:py-16 lg:py-20">
            <div className="md:text-center">
                <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <Title
                        title={t('landing.gym.benefits.title')}
                        showSeparator={true}
                        animated={true}
                    />
                </div>
            </div>
            <div className="container mx-auto mt-12 px-4 sm:px-6 lg:px-8">
                <VideoCarousel
                    data={benefits}
                    videoUrl={videoUrl}
                    renderContent={(item) => (
                        <>
                            <Badge variant="secondary" className="mb-3">
                                {item.badge}
                            </Badge>
                            <h3 className="text-2xl md:text-3xl font-bold mb-3">{item.title}</h3>
                            <p className="text-base md:text-lg mb-2">{item.subtitle}</p>
                            <p className="text-base md:text-lg opacity-80">{item.description}</p>
                        </>
                    )}
                />
            </div>
        </section>
    );
} 