import { convexAuth } from "@convex-dev/auth/server";
import Google from "@auth/core/providers/google";
import { Password } from "@convex-dev/auth/providers/Password";
import WebAuthn from "@auth/core/providers/webauthn";
import { ResendOTPPasswordReset, ResendOTP } from "./ResendOTP";
import { Doc } from "./_generated/dataModel";

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [
    Google({
      profile: (profile) => {
        return {
          id: profile.sub,
          email: profile.email,
          fallbackAvatarUrl: profile.picture,
          name: profile.name,
          firstName: profile.given_name,
          lastName: profile.family_name,
          roles: ["customer"],
          createdAt: new Date().toISOString(),
          provider: "google",
          providerId: profile.sub,
        } as Partial<Doc<'users'>>
      }
    }),
    Password({
      reset: ResendOTPPasswordReset,
      profile: (profile) => {
        return {
          email: profile.email,
          roles: ["customer"],
          createdAt: new Date().toISOString(),
        } as Doc<'users'>
      }
    }),
    WebAuthn,
    ResendOTP,
  ], callbacks: {
    redirect: async ({ redirectTo }) => {
      return redirectTo;
    }
  }
});
