"use client";

import { Card, CardImage } from "@curatd/ui/components/card";
import {
    Carousel,
    CarouselContent,
    CarouselItem,
} from "@curatd/ui/components/carousel";
import { cn } from "@curatd/ui/lib/utils";
import type { CarouselApi } from "@curatd/ui/components/carousel";
import { useI18n } from "@curatd/shared/locales/client";
import { Title } from "./title";
import React from "react";

export function CarouselSection() {
    const [api, setApi] = React.useState<CarouselApi | null>(null);
    const [current, setCurrent] = React.useState(0);
    const [count, setCount] = React.useState(0);
    const t = useI18n();

    React.useEffect(() => {
        if (!api) return;

        setCount(api.scrollSnapList().length);
        setCurrent(api.selectedScrollSnap() + 1);

        api.on("select", () => {
            setCurrent(api.selectedScrollSnap() + 1);
        });
    }, [api]);

    const carouselData = [
        {
            image:
                "/strength.jpg",
            title: t("landing.carousel.items.strength.title"),
            subtitle: t("landing.carousel.items.strength.subtitle"),
        },
        {
            image:
                "/cardio.jpg",
            title: t("landing.carousel.items.cardio.title"),
            subtitle: t("landing.carousel.items.cardio.subtitle"),
        },
        {
            image:
                "/flow.jpg",
            title: t("landing.carousel.items.flow.title"),
            subtitle: t("landing.carousel.items.flow.subtitle"),
        },
        {
            image:
                "/relax.jpg",
            title: t("landing.carousel.items.relax.title"),
            subtitle: t("landing.carousel.items.relax.subtitle"),
        }
    ];

    // Embla Carousel options optimized for mobile touch
    const carouselOptions = {
        align: "start" as const,
        containScroll: "trimSnaps" as const,
        dragFree: false,
        loop: false,
        skipSnaps: false,
        speed: 15,
        slidesToScroll: 1,
        // Touch-specific optimizations
        inViewThreshold: 0.7,
        watchDrag: true,
        watchResize: true,
        watchSlides: true,
        watchSlidesProgress: true,
        // Performance optimizations
        duration: 20,
        startOnIdle: false,
        stopOnInteraction: true,
        stopOnMouseEnter: false,
        stopOnPointerEnter: false,
    };

    return (
        <>
            <style>{`
                .carousel-container {
                    -webkit-overflow-scrolling: touch;
                    scroll-behavior: smooth;
                    overscroll-behavior-x: contain;
                }
                
                .carousel-item {
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    user-select: none;
                    -webkit-touch-callout: none;
                    -webkit-tap-highlight-color: transparent;
                }
                
                .carousel-card {
                    -webkit-transform: translateZ(0);
                    transform: translateZ(0);
                    backface-visibility: hidden;
                    perspective: 1000px;
                }
            `}</style>
            <section id="carousel" aria-label="Fitness session types">
                <div className="mx-auto py-8 sm:py-12 md:py-16 lg:py-20">
                    <Carousel
                        className="w-full carousel-container"
                        setApi={setApi}
                        opts={carouselOptions}
                    >
                        {/* Title */}
                        <div className="px-4 sm:px-6 md:px-8 lg:px-12 xl:px-20">
                            <Title
                                title={t("landing.carousel.title")}
                                showSeparator={true}
                                animated={true}
                                as="h1"
                            />
                        </div>

                        {/* Carousel Items */}
                        <CarouselContent className="gap-2 sm:gap-3 md:gap-4 pl-4 sm:pl-6 md:pl-8 lg:pl-12 xl:pl-20 pb-6 sm:pb-8 md:pb-10">
                            {carouselData.map((item, index) => (
                                <CarouselItem
                                    key={index}
                                    className="basis-auto carousel-item"
                                >
                                    <Card className="h-80 w-48 sm:h-90 sm:w-56 md:h-96 md:w-64 lg:h-120 lg:w-80 relative overflow-hidden group carousel-card">
                                        <CardImage
                                            src={item.image}
                                            alt={`${item.title} - ${item.subtitle}`}
                                            className="object-cover"
                                            loading="lazy"
                                            width={320}
                                            height={480}
                                        />
                                        {/* Gradient overlay */}
                                        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black" aria-hidden="true"></div>
                                        {/* Text overlay */}
                                        <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 md:p-6 text-shadow-lg">
                                            <h2 className="text-primary-foreground text-sm sm:text-base md:text-lg lg:text-xl font-bold mb-1 sm:mb-2 line-clamp-2">
                                                {item.title}
                                            </h2>
                                            <p className="text-xs sm:text-sm text-primary-foreground">
                                                {item.subtitle}
                                            </p>
                                        </div>
                                    </Card>
                                </CarouselItem>
                            ))}
                        </CarouselContent>
                    </Carousel>

                    {/* Carousel Indicators */}
                    {count > 1 && (
                        <div className="flex justify-center gap-2 px-4 mt-4" role="tablist" aria-label="Carousel navigation">
                            {Array.from({ length: count }, (_, index) => (
                                <button
                                    key={index}
                                    className={cn(
                                        "w-2 h-2 sm:w-2.5 sm:h-2.5 rounded-full transition-all duration-300 hover:scale-110",
                                        current === index + 1
                                            ? "bg-primary scale-125"
                                            : " bg-muted-foreground hover:bg-muted/80 active:bg-muted/60"
                                    )}
                                    onClick={() => api?.scrollTo(index)}
                                    aria-label={`Go to slide ${index + 1}`}
                                    role="tab"
                                    aria-selected={current === index + 1}
                                    tabIndex={current === index + 1 ? 0 : -1}
                                />
                            ))}
                        </div>
                    )}
                </div>
            </section>
        </>
    );
}
