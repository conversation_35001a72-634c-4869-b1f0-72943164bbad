"use client";

import { SWRConfig } from "swr";

interface SWRProviderProps {
  children: React.ReactNode;
}

export function SWRProvider({ children }: SWRProviderProps) {
  return (
    <SWRConfig
      value={{
        // User-facing apps can be more aggressive with caching
        revalidateIfStale: false,
        revalidateOnFocus: false,
        revalidateOnReconnect: true,

        // Error handling
        shouldRetryOnError: true,
        errorRetryCount: 2, // Less retries for user-facing app
        errorRetryInterval: 2000,

        // Performance optimizations for user experience
        dedupingInterval: 5000, // Longer deduping for better performance
        focusThrottleInterval: 10000,

        // User-friendly error handler
        onError: (error) => {
          console.error("Data loading error:", error);
          // Could show user-friendly error toast here
        },
      }}
    >
      {children}
    </SWRConfig>
  );
}
