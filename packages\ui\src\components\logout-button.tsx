"use client";

import { useI18n } from "@curatd/shared/locales/client";
import { Button } from "@curatd/ui/components/button";
import { useRouter } from "next/navigation";
import { useAuthActions } from "@convex-dev/auth/react";

export function LogoutButton() {
  const router = useRouter();
  const t = useI18n();
  const { signOut } = useAuthActions();
  const logout = async () => {
    await signOut();
    router.push("/auth/login");
  };

  return (
    <Button className="w-full" onClick={logout}>
      {t("logout.title")}
    </Button>
  );
}
