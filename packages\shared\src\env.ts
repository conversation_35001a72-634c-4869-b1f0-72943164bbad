import { z } from 'zod';

// Cross-platform environment schema that accepts both Next.js and Expo prefixes
export const ConvexEnvSchema = z.object({
    CONVEX_URL: z.string().url('Invalid Convex URL'),
    // Next.js client-side variables
    NEXT_PUBLIC_CONVEX_URL: z.string().url('Invalid Convex URL').optional(),
    // Expo client-side variables
    EXPO_PUBLIC_CONVEX_URL: z.string().url('Invalid Convex URL').optional(),
});

// Cross-platform Stripe schema
export const StripeEnvSchema = z.object({
    STRIPE_SECRET_KEY: z.string().min(1, 'Stripe secret key is required'),
    STRIPE_WEBHOOK_SECRET: z.string().min(1, 'Stripe webhook secret is required'),
    STRIPE_CLIENT_ID: z.string().min(1).optional(),
    // Next.js client-side variables
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string().min(1, 'Stripe publishable key is required').optional(),
    // Expo client-side variables
    EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string().min(1, 'Stripe publishable key is required').optional(),
});

// Resend schema
export const ResendEnvSchema = z.object({
    RESEND_API_KEY: z.string().min(1, 'Resend API key is required'),
});

// Combined schema for full validation
export const AppEnvSchema = ConvexEnvSchema.merge(StripeEnvSchema).merge(ResendEnvSchema);

export const AppUrlsEnvSchema = z.object({
    NEXT_PUBLIC_WEB_APP_URL: z.string().url('Invalid app URL'),
    NEXT_PUBLIC_ADMIN_APP_URL: z.string().url('Invalid admin URL'),
});

// Helper function to get environment variable with cross-platform fallback
export function getEnvVar(nextPublicKey: string, expoPublicKey: string, env: Record<string, string | undefined> = process.env): string {
    const nextValue = env[nextPublicKey];
    const expoValue = env[expoPublicKey];

    if (nextValue) return nextValue;
    if (expoValue) return expoValue;

    throw new Error(`Environment variable not found: ${nextPublicKey} or ${expoPublicKey}`);
}

// Helper function to get Convex client environment variables
export function getConvexClientEnv(env: Record<string, string | undefined> = process.env) {
    return {
        url: getEnvVar('NEXT_PUBLIC_CONVEX_URL', 'EXPO_PUBLIC_CONVEX_URL', env),
    };
}

// Helper function to get Stripe client environment variables  
export function getStripeClientEnv(env: Record<string, string | undefined> = process.env) {
    return {
        publishableKey: getEnvVar('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY', 'EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY', env),
    };
}

// Individual validation functions
export function validateConvexEnv(env: Record<string, string | undefined>) {
    try {
        return ConvexEnvSchema.parse(env);
    } catch (error) {
        if (error instanceof z.ZodError) {
            const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
            throw new Error(`Convex environment validation failed:\n${messages.join('\n')}`);
        }
        throw error;
    }
}

export function validateStripeEnv(env: Record<string, string | undefined>) {
    try {
        return StripeEnvSchema.parse(env);
    } catch (error) {
        if (error instanceof z.ZodError) {
            const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
            throw new Error(`Stripe environment validation failed:\n${messages.join('\n')}`);
        }
        throw error;
    }
}

export function validateResendEnv(env: Record<string, string | undefined>) {
    try {
        return ResendEnvSchema.parse(env);
    } catch (error) {
        if (error instanceof z.ZodError) {
            const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
            throw new Error(`Resend environment validation failed:\n${messages.join('\n')}`);
        }
        throw error;
    }
}

export function validateAppEnv(env: Record<string, string | undefined>) {
    try {
        return AppEnvSchema.parse(env);
    } catch (error) {
        if (error instanceof z.ZodError) {
            const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
            throw new Error(`Environment validation failed:\n${messages.join('\n')}`);
        }
        throw error;
    }
}

export function validateAppUrlsEnv(env: Record<string, string | undefined>) {
    try {
        return AppUrlsEnvSchema.parse(env);
    } catch (error) {
        if (error instanceof z.ZodError) {
            const messages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
            throw new Error(`App URLs environment validation failed:\n${messages.join('\n')}`);
        }
        throw error;
    }
}

// Helper function to get validated environment variables
export function getValidatedEnv() {
    if (typeof process === 'undefined') {
        throw new Error('Environment validation can only be used in Node.js environment');
    }

    return validateAppEnv(process.env);
}

// Type exports
export type ConvexEnv = z.infer<typeof ConvexEnvSchema>;
export type StripeEnv = z.infer<typeof StripeEnvSchema>;
export type ResendEnv = z.infer<typeof ResendEnvSchema>;
export type AppEnv = z.infer<typeof AppEnvSchema>;
export type AppUrlsEnv = z.infer<typeof AppUrlsEnvSchema>;