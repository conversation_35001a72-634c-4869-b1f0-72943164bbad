"use client";

import { useState, useMemo } from "react";
import { usePathname } from "next/navigation";
import { useI18n } from "@curatd/shared/locales/client";
import useCurrentUser from "@curatd/ui/hooks/use-current-user";
import { IconHome, IconUser, IconBuilding } from "@tabler/icons-react";
import { useUser } from "@curatd/shared/backend/use-cases/user-management/client";

export type BusinessEntity = "coach" | "facility_manager";

export interface BusinessEntityInfo {
    type: BusinessEntity;
    id: string;
    displayName: string;
}

export function useBusinessNavigation() {
    const t = useI18n();
    const currentUser = useCurrentUser();
    const { data: user } = useUser(currentUser.id ?? null);
    const pathname = usePathname();

    // Determine available business entities from user data
    const availableEntities = useMemo((): BusinessEntityInfo[] => {
        const entities: BusinessEntityInfo[] = [];
        if (!user) return entities;

        if (user.user_roles?.some((ur) => ur.roles?.code === "coach") && user.coach_profiles) {
            entities.push({
                type: "coach",
                id: user.coach_profiles.id || "unknown",
                displayName: t("business.entityDisplay.coach"),
            });
        }

        if (user.user_roles?.some((ur) => ur.roles?.code === "facility_manager") && user.facility_manager_profiles) {
            entities.push({
                type: "facility_manager",
                id: user.facility_manager_profiles.id || "unknown",
                displayName: t("business.entityDisplay.facilityManager"),
            });
        }

        return entities;
    }, [user, t]);

    // Auto-determine active entity based on current path or default to first available
    const getActiveEntityFromPath = (): BusinessEntity | null => {
        if (pathname.includes("/app/business/coach")) return "coach";
        if (pathname.includes("/app/business/facility-manager")) return "facility_manager";
        return availableEntities[0]?.type || null;
    };

    const [activeEntity, setActiveEntity] = useState<BusinessEntity | null>(getActiveEntityFromPath);

    // Get current active entity info
    const activeEntityInfo = useMemo(() => {
        return availableEntities.find(entity => entity.type === activeEntity) || null;
    }, [availableEntities, activeEntity]);

    // Generate dynamic navigation based on active entity
    const navItems = useMemo(() => {
        const baseItems = [
            {
                title: t("business.nav.dashboard"),
                url: `/app/business`,
                icon: IconHome,
                isActive: pathname === `/app/business` || pathname === `/app/business/`,
            },
        ];

        if (!activeEntity) return baseItems;

        const entityPath = activeEntity === "coach" ? "coach" : "facility-manager";
        const entityItems = [
            {
                title: activeEntity === "coach" ? t("business.nav.coach") : t("business.nav.facilityManager"),
                url: `/app/business/${entityPath}`,
                icon: activeEntity === "coach" ? IconUser : IconBuilding,
                isActive: pathname.startsWith(`/app/business/${entityPath}`),
                items: [
                    {
                        title: t("business.nav.generalProfile"),
                        url: `/app/business/${entityPath}/general-profile`,
                        isActive: pathname === `/app/business/${entityPath}/general-profile`,
                    },
                    {
                        title: t("business.nav.otherPage"),
                        url: `/app/business/${entityPath}/other-page`,
                        isActive: pathname === `/app/business/${entityPath}/other-page`,
                    },
                ],
            },
        ];

        return [...baseItems, ...entityItems];
    }, [activeEntity, pathname, t]);

    const isNavItemActive = (url: string) => {
        return pathname === url || pathname.startsWith(url + '/');
    };

    const switchEntity = (entityType: BusinessEntity) => {
        if (availableEntities.some(e => e.type === entityType)) {
            setActiveEntity(entityType);
        }
    };

    return {
        availableEntities,
        activeEntity,
        activeEntityInfo,
        navItems,
        isNavItemActive,
        switchEntity,
        hasMultipleEntities: availableEntities.length > 1,
    };
} 