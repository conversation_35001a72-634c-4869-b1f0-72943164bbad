"use client";

import { usePathname } from "next/navigation";
import { Separator } from "@curatd/ui/components/separator";
import { SidebarTrigger } from "@curatd/ui/components/sidebar";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@curatd/ui/components/breadcrumb";
import { ThemeSwitcher } from "@curatd/ui/components/theme-switcher";
import LanguageSwitcher from "./language-switcher";

export interface SiteHeaderNavItem {
  title: string;
  url: string;
  icon?: React.ComponentType<any>;
  isActive?: boolean;
  items?: Array<{
    title: string;
    url: string;
    isActive?: boolean;
  }>;
}

export interface SiteHeaderBaseProps {
  navItems: SiteHeaderNavItem[];
  isNavItemActive: (itemUrl: string, pathname: string) => boolean;
  rootBreadcrumb: {
    title: string;
    url: string;
  };
}

export function SiteHeaderBase({
  navItems,
  isNavItemActive,
  rootBreadcrumb,
}: SiteHeaderBaseProps) {
  const pathname = usePathname();

  // Find current navigation item based on pathname
  const currentNavItem = navItems.find((item) =>
    isNavItemActive(item.url, pathname)
  );

  // Generate breadcrumb items
  const generateBreadcrumbs = () => {
    const breadcrumbs = [];

    // Always start with the provided root
    breadcrumbs.push({
      title: rootBreadcrumb.title,
      url: rootBreadcrumb.url,
      isRoot: true,
    });

    // Add current page if it's not the root
    if (currentNavItem && currentNavItem.url !== rootBreadcrumb.url) {
      breadcrumbs.push({
        title: currentNavItem.title,
        url: currentNavItem.url,
        isRoot: false,
      });
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />

        {/* Breadcrumb Navigation */}
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbs.map((breadcrumb, index) => (
              <div key={breadcrumb.url} className="flex items-center gap-1.5">
                <BreadcrumbItem>
                  {index === breadcrumbs.length - 1 ? (
                    <BreadcrumbPage>{breadcrumb.title}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink href={breadcrumb.url}>
                      {breadcrumb.title}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
              </div>
            ))}
          </BreadcrumbList>
        </Breadcrumb>

        <div className="ml-auto flex gap-2">
          <LanguageSwitcher />
          <ThemeSwitcher variant="compact" />
        </div>
      </div>
    </header>
  );
}
