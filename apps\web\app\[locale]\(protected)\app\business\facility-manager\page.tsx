import { redirect } from "next/navigation";
import { getCurrentUser } from "@curatd/shared/backend/use-cases/user-management/server";
import { FacilityManagerDashboardClient } from "@/components/business/facility-manager-dashboard-client";

export default async function FacilityManagerDashboardPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const user = await getCurrentUser();
  const params = await searchParams;

  // Handle the redirect from Stripe Connect onboarding
  if (params?.stripe_return === "true") {
    console.log("stripe_return");
    return redirect("/app/business/facility-manager?onboarded=true");
  }

  // For refresh URLs, redirect to the dashboard with an error
  if (params?.stripe_refresh === "true") {
    return redirect(
      "/app/business/facility-manager?onboarded=false&error=refresh"
    );
  }

  return (
    <FacilityManagerDashboardClient
      user={user}
      facilityManagerProfile={user.facility_manager_profiles}
    />
  );
}
