"use client";

import { motion } from "motion/react";
import { useI18n } from "@curatd/shared/locales/client";
import { PreRegistrationForm } from "../pre-registration-form";
import { Title } from "../title";

export function CoachSignUpSection() {
    const t = useI18n();

    return (
        <section id="join-now" className="py-8 sm:py-12 md:py-16 lg:py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div
                    className="text-center mb-16"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                >
                    <Title
                        title={t('landing.coach.signup.title')}
                        subtitle={t('landing.coach.signup.subtitle')}
                        showSeparator={true}
                        animated={true}
                    />
                </motion.div>

                <motion.div
                    className="max-w-2xl mx-auto"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                    viewport={{ once: true }}
                >
                    <PreRegistrationForm
                        type="coach"    
                        formTitle={t('landing.coach.signup.formTitle')}
                        formDescription={t('landing.coach.signup.formDescription')}
                        className="pt-6"
                    />

                    <div className="mt-8 p-4 bg-muted/50 rounded-lg max-w-2xl mx-auto">
                        <p className="text-sm text-muted-foreground text-center">
                            {t('landing.coach.signup.privacyNote')}
                        </p>
                    </div>
                </motion.div>
            </div>
        </section>
    );
} 