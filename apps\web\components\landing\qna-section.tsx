"use client";

import { Button } from "@curatd/ui/components/button";
import { useI18n } from "@curatd/shared/locales/client";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@curatd/ui/components/accordion";
import { Title } from "./title";
import Link from "next/link";

interface QNAItem {
    question: string;
    answer: string;
}

export function QNASection() {
    const t = useI18n();

    const qnaData: QNAItem[] = [
        {
            question: t("landing.faq.questions.inclusivity.question"),
            answer: t("landing.faq.questions.inclusivity.answer")
        },
        {
            question: t("landing.faq.questions.howItWorks.question"),
            answer: t("landing.faq.questions.howItWorks.answer")
        },
        {
            question: t("landing.faq.questions.locations.question"),
            answer: t("landing.faq.questions.locations.answer")
        },
        {
            question: t("landing.faq.questions.workoutTypes.question"),
            answer: t("landing.faq.questions.workoutTypes.answer")
        },
        {
            question: t("landing.faq.questions.equipment.question"),
            answer: t("landing.faq.questions.equipment.answer")
        },
        {
            question: t("landing.faq.questions.companions.question"),
            answer: t("landing.faq.questions.companions.answer")
        },
        {
            question: t("landing.faq.questions.cancellation.question"),
            answer: t("landing.faq.questions.cancellation.answer")
        },
        {
            question: t("landing.faq.questions.pricing.question"),
            answer: t("landing.faq.questions.pricing.answer")
        }
    ];

    return (
        <section id="faq" className="py-8 sm:py-12 md:py-16 lg:py-20">
            <div className="max-w-4xl mx-auto">
                <div className="text-center mb-8 sm:mb-12 md:mb-16">
                    <Title
                        title={t("landing.faq.title")}
                        subtitle={t("landing.faq.subtitle")}
                        showSeparator={true}
                        animated={true}
                    />
                </div>

                <Accordion type="multiple" className="rounded-xl border bg-card/80 shadow-sm overflow-hidden">
                    {qnaData.map((item, index) => (
                        <div key={index}>
                            {index !== 0 && (
                                <div className="mx-4 sm:mx-6 border-t border-border/60 dark:border-border/40 opacity-80" />
                            )}
                            <AccordionItem value={String(index)} className="border-0">
                                <AccordionTrigger className="px-4 sm:px-6 py-5 text-left font-bold text-base sm:text-lg transition-colors duration-200 hover:bg-muted/40 focus-visible:bg-muted/40 rounded-none">
                                    {item.question}
                                </AccordionTrigger>
                                <AccordionContent className="px-4 sm:px-6 pb-5 pt-0 text-muted-foreground text-sm sm:text-base leading-relaxed">
                                    {item.answer}
                                </AccordionContent>
                            </AccordionItem>
                        </div>
                    ))}
                </Accordion>

                <div className="text-center mt-8 sm:mt-10 md:mt-12">
                    <p className="mb-3 sm:mb-4 text-sm sm:text-base px-4 sm:px-0">
                        {t("landing.faq.contactSubtitle")}
                    </p>
                    <Link href="/contact">
                        <Button className="bg-foreground text-background sm:py-5 text-base sm:text-lg font-semibold hover:bg-foreground/90 touch-manipulation active:scale-95 transition-all duration-200 ">
                            {t("landing.faq.contactCta")}
                        </Button>
                    </Link>
                </div>
            </div>
        </section>
    );
} 