import { useRef, useState, useEffect, ReactNode } from "react";
import { motion, AnimatePresence } from "motion/react";
import {
    Carousel,
    CarouselContent,
    CarouselItem,
} from "@curatd/ui/components/carousel";

interface CarouselItem {
    title?: string;
    subtitle?: string;
    description?: string;
    badge?: ReactNode;
    [key: string]: any;
}

interface VideoCarouselProps {
    data: CarouselItem[];
    videoUrl: string;
    renderContent: (item: CarouselItem, index: number) => ReactNode;
    className?: string;
    indicatorClassName?: string;
    autoAdvanceMs?: number;
}

// Responsive breakpoints (tailwindcss default)
const breakpoints = {
    xxl: 1536,
    xl: 1280,
    md: 768,
};

function useResponsiveCards(itemsCount: number) {
    const [cards, setCards] = useState(1);
    useEffect(() => {
        function updateCards() {
            const width = window.innerWidth;
            if (width >= breakpoints.xxl) setCards(4 < itemsCount ? 4 : itemsCount);
            else if (width >= breakpoints.xl) setCards(3 < itemsCount ? 3 : itemsCount);
            else if (width >= breakpoints.md) setCards(2 < itemsCount ? 2 : itemsCount);
            else setCards(1);
        }
        updateCards();
        window.addEventListener("resize", updateCards);
        return () => window.removeEventListener("resize", updateCards);
    }, []);
    return cards;
}

export function VideoCarousel({
    data,
    videoUrl,
    renderContent,
    className = "",
    indicatorClassName = "",
    autoAdvanceMs = 5000,
}: VideoCarouselProps) {
    const [activeIndex, setActiveIndex] = useState(0);
    const [direction, setDirection] = useState(0); // 1 for next, -1 for prev
    const touchStartX = useRef<number | null>(null);
    const touchEndX = useRef<number | null>(null);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    const itemsCount = data.length;
    const visibleCards = useResponsiveCards(itemsCount);

    // State for desktop carousel
    const [carouselApi, setCarouselApi] = useState<any>(null);
    const [desktopCurrent, setDesktopCurrent] = useState(0);
    const [desktopCount, setDesktopCount] = useState(0);
    const [desktopDirection, setDesktopDirection] = useState<'right' | 'left'>('right');

    const startAutoAdvance = () => {
        if (intervalRef.current) clearInterval(intervalRef.current);
        intervalRef.current = setInterval(() => {
            setDirection(1);
            setActiveIndex((current) => (current + 1) % itemsCount);
        }, autoAdvanceMs);
    };

    useEffect(() => {
        startAutoAdvance();
        return () => {
            if (intervalRef.current) clearInterval(intervalRef.current);
        };
    }, [itemsCount, autoAdvanceMs]);

    useEffect(() => {
        if (!carouselApi) return;
        setDesktopCount(carouselApi.scrollSnapList().length);
        setDesktopCurrent(carouselApi.selectedScrollSnap());
        const onSelect = () => setDesktopCurrent(carouselApi.selectedScrollSnap());
        carouselApi.on("select", onSelect);
        return () => carouselApi.off("select", onSelect);
    }, [carouselApi]);

    // Desktop auto-slide: alternate left/right every 5s
    useEffect(() => {
        if (!carouselApi) return;
        if (visibleCards === 1) return; // Only run on desktop
        const interval = setInterval(() => {
            if (desktopDirection === 'right') {
                carouselApi.scrollNext();
                setDesktopDirection('left');
            } else {
                carouselApi.scrollPrev();
                setDesktopDirection('right');
            }
        }, 5000);
        return () => clearInterval(interval);
    }, [carouselApi, desktopDirection, visibleCards]);

    const goToSlide = (index: number) => {
        setDirection(index > activeIndex ? 1 : -1);
        setActiveIndex(index);
        startAutoAdvance();
    };

    const handleTouchStart = (e: React.TouchEvent) => {
        if (e.touches && e.touches[0]) touchStartX.current = e.touches[0].clientX;
    };
    const handleTouchMove = (e: React.TouchEvent) => {
        if (e.touches && e.touches[0]) touchEndX.current = e.touches[0].clientX;
    };
    const handleTouchEnd = () => {
        if (typeof touchStartX.current !== "number" || typeof touchEndX.current !== "number") return;
        const diff = touchStartX.current - touchEndX.current;
        const threshold = 50;
        if (Math.abs(diff) > threshold) {
            if (diff > 0) {
                setDirection(1);
                setActiveIndex((current) => (current + 1) % itemsCount);
            } else {
                setDirection(-1);
                setActiveIndex((current) => (current - 1 + itemsCount) % itemsCount);
            }
            startAutoAdvance();
        }
        touchStartX.current = null;
        touchEndX.current = null;
    };



    return (
        <div
            className={`relative w-full h-[50vh] md:h-[60vh] overflow-hidden rounded-xl touch-manipulation ${className}`}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
        >
            {/* Video background */}
            <video
                autoPlay
                loop
                muted
                playsInline
                className="absolute inset-0 object-cover w-full h-full"
            >
                <source src={videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
            </video>
            <div className="absolute inset-0 bg-black/40"></div>

            {/* Mobile carousel (1 card) */}
            {visibleCards === 1 && (
                <div className="absolute bottom-0 left-0 right-0 flex flex-row justify-center p-6 md:hidden h-fit">
                    <AnimatePresence initial={false} custom={direction}>
                        {data[activeIndex] && (
                            <motion.div
                                key={activeIndex}
                                custom={direction}
                                variants={{
                                    enter: (dir: number) => ({
                                        opacity: 0,
                                        x: dir > 0 ? 100 : -100,
                                        position: 'absolute',
                                        width: '100%',
                                    }),
                                    center: {
                                        opacity: 1,
                                        x: 0,
                                        position: 'relative',
                                        width: '100%',
                                        transition: {
                                            type: "spring",
                                            stiffness: 300,
                                            damping: 30,
                                            opacity: { duration: 0.2 },
                                        },
                                    },
                                    exit: (dir: number) => ({
                                        opacity: 0,
                                        x: dir > 0 ? -100 : 100,
                                        position: 'absolute',
                                        width: '100%',
                                        transition: {
                                            type: "spring",
                                            stiffness: 300,
                                            damping: 30,
                                            opacity: { duration: 0.2 },
                                        },
                                    }),
                                }}
                                initial="enter"
                                animate="center"
                                exit="exit"
                                className="carousel-content bg-background/80 backdrop-blur-sm p-6 rounded-xl w-full h-fit max-w-md min-h-[120px] mx-auto pointer-events-auto"
                            >
                                {renderContent(data[activeIndex], activeIndex)}
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>
            )}

            {/* Responsive carousel for >= md (2, 3, or 4 cards) */}
            {visibleCards > 1 && (
                <div className="absolute bottom-0 left-0 right-0 h-fit hidden md:block p-6">
                    <Carousel
                        className="w-full"
                        setApi={setCarouselApi}
                        opts={{ slidesToScroll: 1, containScroll: 'trimSnaps', dragFree: false }}
                    >
                        <CarouselContent className="flex">
                            {data.map((item, index) => (
                                <CarouselItem
                                    key={index}
                                    className=""
                                    style={{ flex: `0 0 calc(100% / ${visibleCards})`, maxWidth: `calc(100% / ${visibleCards})` }}
                                >
                                    <div className="carousel-content bg-background/80 backdrop-blur-sm p-6 rounded-xl h-full">
                                        {renderContent(item, index)}
                                    </div>
                                </CarouselItem>
                            ))}
                        </CarouselContent>
                    </Carousel>
                    {/* Desktop indicators */}
                    {!(visibleCards === itemsCount) && (
                    <div className="flex justify-center gap-2 mt-4">
                        {Array.from({ length: data.length - visibleCards + 1 }, (_, index) => (
                            <button
                                key={index}
                                className={`w-2.5 h-2.5 rounded-full transition-all ${activeIndex === index ? "bg-primary-foreground w-6" : "bg-primary-foreground/40"}`}
                                onClick={() => goToSlide(index)}
                                aria-label={`Go to slide ${index + 1}`}
                            />
                        ))}
                    </div>
                    )}
                </div>
            )}

            {/* Indicators mobile */}
            {visibleCards === 1 && (
                <div
                    className={`md:hidden absolute bottom-2 left-0 right-0 flex justify-center gap-2 mt-4 ${indicatorClassName}`}
                >
                    {data.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => goToSlide(index)}
                            className={`w-2.5 h-2.5 rounded-full transition-all ${activeIndex === index ? "bg-primary-foreground w-6" : "bg-primary-foreground/40"}`}
                            aria-label={`Go to slide ${index + 1}`}
                        />
                    ))}
                </div>
            )}
        </div >
    );
}
