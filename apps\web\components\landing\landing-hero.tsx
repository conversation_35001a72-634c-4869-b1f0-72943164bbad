"use client";

import { ReactNode } from "react";
import { But<PERSON> } from "@curatd/ui/components/button";
import { motion, useScroll, useTransform } from "motion/react";

export interface LandingHeroProps {
    title: ReactNode;
    subtitle?: ReactNode;
    ctaText?: string;
    onCtaClick?: () => void;
    backgroundVideoSrc?: string;
    backgroundImageSrc?: string;
    overlayClassName?: string;
    logo?: ReactNode;
    mobileContent?: ReactNode;
    desktopContent?: ReactNode;
    className?: string;
    ctaClassName?: string;
    children?: ReactNode;
}

export function LandingHero({
    title,
    subtitle,
    ctaText,
    onCtaClick,
    backgroundVideoSrc,
    backgroundImageSrc,
    overlayClassName = "bg-black/40",
    logo,
    mobileContent,
    desktopContent,
    className = "",
    ctaClassName = "",
    children,
}: LandingHeroProps) {
    // Parallax effect for video/image background
    const { scrollYProgress } = useScroll();
    const videoY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
    const contentY = useTransform(scrollYProgress, [0, 1], ["0%", "100%"]);
    const contentOpacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

    return (
        <section
            className={`relative min-h-screen flex items-center justify-center overflow-hidden ${className}`}
        >
            {/* Background Video or Image with Parallax */}
            <motion.div
                className="absolute inset-0 z-0 overflow-hidden"
                style={{ y: videoY }}
            >
                <div className="relative w-full h-[120%]" style={{ willChange: "transform" }}>
                    {backgroundVideoSrc ? (
                        <motion.video
                            className="w-full h-full object-cover"
                            src={backgroundVideoSrc}
                            autoPlay
                            muted
                            loop
                            playsInline
                            aria-label="Background video showcasing fitness activities"
                            preload="metadata"
                            initial={{ scale: 1.1, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 2, ease: "easeOut" }}
                        >
                            <track kind="captions" src={backgroundVideoSrc} srcLang="en" label="English" />
                            Your browser does not support the video tag.
                        </motion.video>
                    ) : backgroundImageSrc ? (
                        <motion.img
                            className="w-full h-full object-cover"
                            src={backgroundImageSrc}
                            alt="Hero background showcasing fitness activities"
                            loading="eager"
                            width={1920}
                            height={1080}
                            initial={{ scale: 1.1, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 2, ease: "easeOut" }}
                        />
                    ) : null}
                    {/* Overlay for better text readability */}
                    <motion.div
                        className={`absolute inset-0 ${overlayClassName}`}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 1.5, delay: 0.5 }}
                    ></motion.div>
                </div>
            </motion.div>

            {/* Mobile View */}
            <motion.div
                className="absolute inset-0 z-10 flex flex-col items-center justify-center md:hidden"
                style={{ y: contentY, opacity: contentOpacity }}
            >
                {logo}
                {mobileContent ? (
                    mobileContent
                ) : (
                    <motion.div
                        className="px-6 text-center space-y-6 pt-6"
                        initial={{ opacity: 0, y: 50 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 1, delay: 1 }}
                    >
                        <motion.h1
                            className="text-3xl text-primary-foreground text-shadow-[0px_4px_8px_rgb(0_0_0_/_0.3)]"
                            initial={{ opacity: 0, y: -50 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.8, delay: 1.2 }}
                        >
                            {title}
                        </motion.h1>
                        {subtitle && (
                            <motion.p
                                className="text-lg font-light text-primary-foreground/90 text-shadow-[0px_2px_4px_rgb(0_0_0_/_0.3)]"
                                initial={{ opacity: 0, y: 50 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 1.4 }}
                            >
                                {subtitle}
                            </motion.p>
                        )}
                        {ctaText && (
                            <motion.div
                                className="pt-4"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8, delay: 1.6 }}
                            >
                                <Button
                                    size="lg"
                                    className={`bg-primary-foreground text-secondary px-8 py-4 text-base rounded-full font-normal hover:bg-foreground/80 ${ctaClassName}`}
                                    onClick={onCtaClick}
                                >
                                    {ctaText}
                                </Button>
                            </motion.div>
                        )}
                        {children}
                    </motion.div>
                )}
            </motion.div>

            {/* Desktop View */}
            <motion.div
                className="absolute bottom-20 z-10 w-full hidden md:block"
                style={{ y: contentY, opacity: contentOpacity }}
            >
                {desktopContent ? (
                    desktopContent
                ) : (
                    <div className="max-w-7xl flex items-center justify-between px-10">
                        {/* Left side - Main hero content */}
                        <motion.div
                            className="flex-1 lg:max-w-3xl xl:max-w-6xl lg:space-y-10 space-y-6 md:px-10"
                            initial={{ opacity: 0, x: -100 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 1, delay: 1 }}
                        >
                            <motion.h1
                                className="text-4xl lg:text-5xl xl:text-7xl font-bold text-primary-foreground text-shadow-[0px_8px_8px_rgb(0_0_0_/_0.15)]"
                                initial={{ opacity: 0, x: 80 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 1, delay: 1.2 }}
                            >
                                {title}
                            </motion.h1>
                            {subtitle && (
                                <motion.span
                                    className="text-primary-foreground text-xl md:text-2xl lg:text-4xl text-shadow-[0px_8px_8px_rgb(0_0_0_/_0.15)]"
                                    initial={{ opacity: 0, x: 100 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.8, delay: 1.6 }}
                                >
                                    {subtitle}
                                </motion.span>
                            )}
                            {ctaText && (
                                <motion.div
                                    className="flex justify-start pt-10"
                                    initial={{ opacity: 0, y: 50 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.8, delay: 1.8 }}
                                >
                                    <motion.div
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                        transition={{ type: "spring", stiffness: 400, damping: 17 }}
                                    >
                                        <Button
                                            size="lg"
                                            className={`bg-primary-foreground text-secondary px-12 py-6 text-sm md:text-xl rounded-full font-semibold hover:bg-foreground/80 ${ctaClassName}`}
                                            onClick={onCtaClick}
                                        >
                                            {ctaText}
                                        </Button>
                                    </motion.div>
                                </motion.div>
                            )}
                            {children}
                        </motion.div>
                    </div>
                )}
            </motion.div>
        </section>
    );
} 