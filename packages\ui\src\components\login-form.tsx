"use client";

import { cn } from "@curatd/ui/lib/utils";
import { Button } from "@curatd/ui/components/button";
import { useState } from "react";
import { FaGoogle } from "react-icons/fa";
import { useI18n } from "@curatd/shared/locales/client";
import { Label } from "./label";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "./input-otp";
import { ArrowLeft, ShieldCheck } from "lucide-react";
import { Input } from "./input";
import { Separator } from "./separator";
import { useAuthActions } from "@convex-dev/auth/react";
import CuratdLogo from "./curatd-logo";

const Providers = {
  google: {
    name: "Google",
    providerKey: "google",
    icon: FaGoogle,
    color:
      "bg-google text-google-foreground hover:shadow-google/20 hover:bg-google/90 hover:shadow-xl hover:text-google-foreground transition-all duration-200",
  },
};

type AuthStep = "email" | "otp";

export function LoginForm({
  className,
  admin = false,
  redirectTo = "/app",
  ...props
}: React.ComponentPropsWithoutRef<"div"> & {
  redirectTo?: string;
  admin?: boolean;
}) {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [authStep, setAuthStep] = useState<AuthStep>("email");
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState("");
  const t = useI18n();
  const { signIn } = useAuthActions();

  const handleSocialLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    const provider = (
      (e.nativeEvent as SubmitEvent).submitter as HTMLButtonElement
    ).getAttribute("data-provider") as keyof typeof Providers;

    setIsLoading(true);
    setError(null);

    try {
      const { signingIn, redirect } = await signIn(provider, {
        redirectTo: `${window.location.origin}?next=${redirectTo}`,
      });
      if (signingIn) {
        setIsLoading(true);
      }
      if (redirect) {
        window.location.href = redirect.toString();
      }
    } catch (error: unknown) {
      setError(
        error instanceof Error ? error.message : t("login.errors.generic")
      );
      setIsLoading(false);
    }
  };

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      setError(t("login.errors.invalid_email"));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { signingIn, redirect } = await signIn("otp", {
        email: email.trim(),
        redirectTo: `${window.location.origin}/${redirectTo}`,
      });

      if (signingIn) {
        setIsLoading(true);
      }
      if (redirect) {
        window.location.href = redirect.toString();
      }

      setAuthStep("otp");
      setError(null);
    } catch (error: unknown) {
      setError(
        error instanceof Error
          ? error.message
          : t("login.errors.send_otp_failed")
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (otp.length !== 6) {
      setError(t("login.errors.incomplete_otp"));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { signingIn, redirect } = await signIn("otp", {
        email: email.trim(),
        code: otp,
        redirectTo: `${window.location.origin}/auth/oauth?next=${redirectTo}`,
      });

      if (signingIn) {
        setIsLoading(true);
      }
      if (redirect) {
        window.location.href = redirect.toString();
      }
    } catch (error: unknown) {
      setError(
        error instanceof Error
          ? error.message
          : t("login.errors.verify_otp_failed")
      );
      setIsLoading(false);
    }
  };

  const handleBackToEmail = () => {
    setAuthStep("email");
    setOtp("");
    setError(null);
  };

  return (
    <div
      className={cn(
        "w-full mx-auto flex flex-col items-center justify-center flex-1",
        className
      )}
      {...props}
    >
      {/* Main Card with Glass Morphism */}
      <div className="relative bg-card/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-border/20 ring-1 ring-foreground/5 overflow-hidden flex-1 md:flex-none flex flex-col items-center justify-center">
        {/* Subtle Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-background/50 via-background/30 to-primary/5 opacity-50" />

        {/* Content */}
        <div className="relative z-10 p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mb-4">
              {authStep === "email" ? (
                <div className="flex items-center justify-center mb-4">
                  <CuratdLogo variant={admin ? "admin" : "default"} />
                </div>
              ) : (
                <div className="w-16 h-16 mx-auto bg-primary/10 rounded-2xl flex items-center justify-center mb-4">
                  <ShieldCheck className="w-8 h-8 text-primary" />
                </div>
              )}
            </div>

            <h1 className="text-3xl font-bold text-foreground tracking-tight mb-2">
              {admin ? t("login.admin.title") : t("login.title")}
            </h1>

            <p className="text-sm text-foreground font-light">
              {authStep === "email" ? (
                admin ? (
                  t("login.admin.description")
                ) : (
                  t("login.description")
                )
              ) : (
                <>
                  {t("login.otp.description")}{" "}
                  <span className="font-medium text-foreground">{email}</span>
                </>
              )}
            </p>
            {authStep === "email" && (
              <p className="text-sm text-foreground/70 italic font-light">
                {admin
                  ? t("login.admin.subdescription")
                  : t("login.subdescription")}
              </p>
            )}
          </div>

          {authStep === "email" ? (
            <div className="space-y-6">
              {/* Email Form */}
              <form onSubmit={handleEmailSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="email"
                    className="font-medium text-foreground/90"
                  >
                    {t("login.email.label")}
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t("login.email.placeholder")}
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                    required
                    className="h-12 rounded-xl border-border/20 bg-background/50 backdrop-blur-sm focus:ring-primary/20 focus:border-primary/50 transition-all duration-200"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 rounded-xl font-semibold bg-primary hover:bg-primary/90 shadow-lg hover:shadow-xl transition-all duration-200"
                  disabled={isLoading}
                >
                  {isLoading
                    ? t("login.email.sending")
                    : t("login.email.send_code")}
                </Button>
              </form>

              {/* Divider */}
              <div className="relative py-4">
                <div className="absolute inset-0 flex items-center">
                  <Separator className="w-full border-border/30" />
                </div>
                <div className="relative flex justify-center">
                  <span className="bg-card/80 backdrop-blur-sm px-4 text-sm font-medium text-foreground/60 tracking-wide">
                    {t("login.social.continue_with")}
                  </span>
                </div>
              </div>

              {/* Social Login */}
              <form onSubmit={handleSocialLogin}>
                <div className="grid grid-cols-auto gap-4">
                  {Object.values(Providers).map((provider) => (
                    <Button
                      type="submit"
                      variant="outline"
                      disabled={isLoading}
                      data-provider={provider.providerKey}
                      key={provider.providerKey}
                      className={cn(
                        "h-12 rounded-xl text-xs lg:text-base border-border/30 bg-background/30 backdrop-blur-sm font-medium transition-all duration-200",
                        provider.color
                      )}
                    >
                      <provider.icon className="w-5 h-5 mr-2" />
                      {isLoading ? t("login.loading") : provider.name}
                    </Button>
                  ))}
                </div>
              </form>
            </div>
          ) : (
            <div className="space-y-6">
              {/* OTP Form */}
              <form onSubmit={handleOtpSubmit} className="space-y-6">
                <div className="space-y-4">
                  <Label
                    htmlFor="otp"
                    className="font-medium text-foreground/90 block text-center"
                  >
                    {t("login.otp.label")}
                  </Label>

                  <div className="flex justify-center">
                    <InputOTP
                      maxLength={6}
                      value={otp}
                      onChange={(value) => setOtp(value)}
                      disabled={isLoading}
                      className="gap-3"
                    >
                      <InputOTPGroup className="gap-2">
                        <InputOTPSlot
                          index={0}
                          className="w-8 h-8 md:w-12 md:h-12 font-semibold rounded-xl border-border/30 bg-background/50 backdrop-blur-sm focus:ring-primary/20 focus:border-primary/50"
                        />
                        <InputOTPSlot
                          index={1}
                          className="w-8 h-8 md:w-12 md:h-12 font-semibold rounded-xl border-border/30 bg-background/50 backdrop-blur-sm focus:ring-primary/20 focus:border-primary/50"
                        />
                        <InputOTPSlot
                          index={2}
                          className="w-8 h-8 md:w-12 md:h-12 font-semibold rounded-xl border-border/30 bg-background/50 backdrop-blur-sm focus:ring-primary/20 focus:border-primary/50"
                        />
                      </InputOTPGroup>
                      <InputOTPSeparator className="text-foreground/30" />
                      <InputOTPGroup className="gap-2">
                        <InputOTPSlot
                          index={3}
                          className="w-8 h-8 md:w-12 md:h-12 font-semibold rounded-xl border-border/30 bg-background/50 backdrop-blur-sm focus:ring-primary/20 focus:border-primary/50"
                        />
                        <InputOTPSlot
                          index={4}
                          className="w-8 h-8 md:w-12 md:h-12 font-semibold rounded-xl border-border/30 bg-background/50 backdrop-blur-sm focus:ring-primary/20 focus:border-primary/50"
                        />
                        <InputOTPSlot
                          index={5}
                          className="w-8 h-8 md:w-12 md:h-12 font-semibold rounded-xl border-border/30 bg-background/50 backdrop-blur-sm focus:ring-primary/20 focus:border-primary/50"
                        />
                      </InputOTPGroup>
                    </InputOTP>
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 rounded-xl font-semibold bg-primary hover:bg-primary/90 shadow-lg hover:shadow-xl transition-all duration-200"
                  disabled={isLoading}
                >
                  {isLoading ? t("login.otp.verifying") : t("login.otp.verify")}
                </Button>
              </form>

              {/* Back Button */}
              <Button
                type="button"
                variant="ghost"
                className="w-full h-12 rounded-xl font-medium text-foreground/70 hover:text-foreground hover:bg-background/50 transition-all duration-200"
                onClick={handleBackToEmail}
                disabled={isLoading}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {t("login.otp.back")}
              </Button>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mt-6 p-4 rounded-xl bg-destructive/10 border border-destructive/20 backdrop-blur-sm">
              <p className="text-sm font-medium text-destructive text-center">
                {error}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
