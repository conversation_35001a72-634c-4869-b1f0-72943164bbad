"use client";

import React from "react";
import { Title } from "./title";
import { motion } from "motion/react";

export interface HowItWorksStep {
    number: string;
    icon: React.ReactNode;
    title: string;
    description: string;
}

interface HowItWorksSectionGenericProps {
    title: string;
    steps: HowItWorksStep[];
    columns?: number; // default 4
    className?: string;
}

export function HowItWorksSectionGeneric({
    title,
    steps,
    columns = 4,
    className = "",
}: HowItWorksSectionGenericProps) {
    // Determine grid columns
    const gridCols =
        columns === 4
            ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
            : columns === 3
                ? "grid-cols-1 md:grid-cols-3"
                : `grid-cols-1 md:grid-cols-${columns}`;

    return (
        <section className={`relative bg-background py-8 sm:py-12 md:py-16 lg:py-20 ${className}`.trim()}>
            {/* Section Title */}
            <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <Title title={title} showSeparator={true} animated={true} as="h2" />
            </div>
            {/* Steps Cards */}
            <div
                className={`container mx-auto px-4 sm:px-6 lg:px-8 grid ${gridCols} gap-8`}
            >
                {steps.map((step, index) => (
                    <motion.div
                        key={index}
                        className="text-center p-6 rounded-2xl bg-card border border-border hover:border-primary/20 transition-all duration-300 hover:shadow-lg"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8, delay: index * 0.1 }}
                        whileHover={{ y: -5 }}
                    >
                        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-6">
                            {step.icon}
                        </div>
                        <h3 className="text-xl font-semibold text-foreground mb-3">
                            {step.title}
                        </h3>
                        <p className="text-muted-foreground leading-relaxed">{step.description}</p>
                    </motion.div>
                ))}
            </div>
        </section>
    );
} 