"use client";

import { useState } from "react";
import { View, Text, ScrollView, Pressable } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useI18n } from "@curatd/shared/locales/client";
import { RoleSelectionCard, type UserRole } from "./role-selection-card";

interface OnboardingScreenProps {
  onComplete: (selectedRole?: UserRole) => void;
}

export function OnboardingScreen({ onComplete }: OnboardingScreenProps) {
  const t = useI18n();
  const [selectedRole, setSelectedRole] = useState<UserRole | undefined>();

  const roles: Array<{
    role: UserRole;
    title: string;
    description: string;
    features: string[];
  }> = [
    {
      role: "regular_user",
      title: t("onboarding.roleSelection.regularUser.title"),
      description: t("onboarding.roleSelection.regularUser.description"),
      features: [
        t("onboarding.roleSelection.regularUser.features.bookWorkouts"),
        t(
          "onboarding.roleSelection.regularUser.features.findCertifiedTrainers"
        ),
        t("onboarding.roleSelection.regularUser.features.trackProgress"),
        t("onboarding.roleSelection.regularUser.features.joinGroupSessions"),
      ],
    },
    {
      role: "coach",
      title: t("onboarding.roleSelection.coach.title"),
      description: t("onboarding.roleSelection.coach.description"),
      features: [
        t("onboarding.roleSelection.coach.features.createTrainingPrograms"),
        t("onboarding.roleSelection.coach.features.manageSchedule"),
        t("onboarding.roleSelection.coach.features.acceptPayments"),
        t("onboarding.roleSelection.coach.features.buildClientBase"),
      ],
    },
    {
      role: "facility_manager",
      title: t("onboarding.roleSelection.facilityManager.title"),
      description: t("onboarding.roleSelection.facilityManager.description"),
      features: [
        t("onboarding.roleSelection.facilityManager.features.listFacility"),
        t("onboarding.roleSelection.facilityManager.features.manageBookings"),
        t(
          "onboarding.roleSelection.facilityManager.features.partnerWithTrainers"
        ),
        t("onboarding.roleSelection.facilityManager.features.earnRevenue"),
      ],
    },
  ];

  const handleContinue = () => {
    onComplete(selectedRole);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="px-6 pt-8 pb-6">
          <Text className="text-3xl font-bold text-gray-900 text-center mb-4">
            {t("onboarding.title")}
          </Text>
          <Text className="text-lg text-gray-600 text-center">
            {t("onboarding.roleSelection.title")}
          </Text>
          <Text className="text-sm text-gray-500 text-center mt-2">
            {t("onboarding.roleSelection.subtitle")}
          </Text>
        </View>

        {/* Role Cards */}
        <View className="pb-6">
          {roles.map((roleData) => (
            <RoleSelectionCard
              key={roleData.role}
              role={roleData.role}
              title={roleData.title}
              description={roleData.description}
              features={roleData.features}
              isSelected={selectedRole === roleData.role}
              onSelect={setSelectedRole}
              selectText={t("onboarding.roleSelection.buttons.selectRole")}
              selectedText={t("onboarding.roleSelection.buttons.selected")}
            />
          ))}
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View className="p-6 border-t border-gray-200 bg-white">
        <Pressable
          onPress={handleContinue}
          disabled={!selectedRole}
          className={`
            py-4 px-6 rounded-xl items-center justify-center mb-3
            ${selectedRole ? "bg-blue-500" : "bg-gray-300"}
          `}
        >
          <Text
            className={`font-semibold text-lg ${selectedRole ? "text-white" : "text-gray-500"}`}
          >
            {t("onboarding.roleSelection.buttons.continue")}
          </Text>
        </Pressable>
      </View>
    </SafeAreaView>
  );
}
