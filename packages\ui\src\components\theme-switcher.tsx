"use client";

import { useTheme } from "next-themes";
import { Sun, Moon } from "lucide-react";
import { Button } from "@curatd/ui/components/button";
import { cn } from "@curatd/ui/lib/utils";
import { useEffect, useState } from "react";

interface ThemeSwitcherProps {
  variant?: "default" | "compact";
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function ThemeSwitcher({
  variant = "compact",
  size = "sm",
  className,
}: ThemeSwitcherProps) {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Ensure we're mounted on the client
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleThemeToggle = () => {
    if (theme === "light") {
      setTheme("dark");
    } else {
      setTheme("light");
    }
  };

  const getThemeIcon = () => {
    if (theme === "light") {
      return <Sun className="h-4 w-4" />;
    }
    return <Moon className="h-4 w-4" />;
  };

  const getThemeLabel = () => {
    if (theme === "light") {
      return "Light";
    }
    return "Dark";
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size={size}
        className={cn(
          "flex items-center transition-all duration-200",
          variant === "default" ? "space-x-2" : "",
          className
        )}
        disabled
      >
        <Moon className="h-4 w-4" />
        {variant === "default" && (
          <span className="capitalize text-xs font-medium">Dark</span>
        )}
      </Button>
    );
  }

  return (
    <Button
      variant="ghost"
      size={size}
      onClick={handleThemeToggle}
      className={cn(
        "flex items-center transition-all duration-200 hover:scale-105",
        variant === "default" ? "space-x-2" : "",
        className
      )}
      title={`Current theme: ${getThemeLabel()}. Click to toggle.`}
    >
      {getThemeIcon()}
      {variant === "default" && (
        <span className="capitalize text-xs font-medium">
          {getThemeLabel()}
        </span>
      )}
    </Button>
  );
}
