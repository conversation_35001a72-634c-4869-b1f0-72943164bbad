{"name": "@curatd/backend", "version": "0.1.0", "main": "./index.ts", "types": "./index.ts", "scripts": {"build": "pnpm dlx convex codegen", "dev": "pnpm dlx convex dev", "deploy": "pnpm dlx convex deploy"}, "dependencies": {"@auth/core": "^0.40.0", "@convex-dev/auth": "^0.0.87", "@convex-dev/eslint-plugin": "0.0.1-alpha.4", "@curatd/typescript-config": "workspace:*", "@types/node": "^24.0.3", "convex": "^1.25.4", "convex-helpers": "^0.1.99", "oslo": "^1.2.1", "stripe": "^17.7.0", "zod": "^3.25.67"}, "devDependencies": {"cpy-cli": "^5.0.0", "typescript": "5.8.2"}}