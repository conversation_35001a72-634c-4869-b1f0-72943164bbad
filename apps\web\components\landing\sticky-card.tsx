"use client";

import React, { useRef } from "react";
import { motion } from "motion/react";
import { FeatureImage } from "@curatd/ui/components/feature-image";
import { Step } from "./types";

interface StickyCardProps {
    step: Step;
    index: number;
}

export const StickyCard = ({ step, index }: StickyCardProps) => {
    const container = useRef(null);

    return (
        <div ref={container} className="flex items-center justify-center sticky top-[5vh] md:top-[20vh]">
            <motion.div
                style={{
                    top: `calc(-5vh + ${index * 25}px)`
                }}
                className="relative w-screen"
            >
                <div className="relative">
                    <FeatureImage
                        title={step.number + " - " + step.title}
                        description={step.description}
                        subtitle={step.subtitle}
                        image={step.image}
                        left={index % 2 === 0}
                    />
                </div>
            </motion.div>
        </div>
    );
}; 