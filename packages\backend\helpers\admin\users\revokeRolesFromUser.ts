import { MutationCtx } from "../../../convex/_generated/server";
import { Doc, Id } from "../../../convex/_generated/dataModel";
import { Roles } from "../../../types/enums";

/**
 * Helper function to revoke roles from a user and handle role-specific cleanup
 * Following Convex guidelines for helper functions
 */
export async function revokeRolesFromUser(
    ctx: MutationCtx,
    userId: Id<"users">,
    rolesToRemove: Roles[]
): Promise<Partial<Doc<"users">>> {
    const user = await ctx.db.get<"users">(userId);
    if (!user) {
        throw new Error("User not found");
    }

    // Filter out roles to be removed
    const updatedRoles = user.roles.filter(role => !rolesToRemove.includes(role));

    // If no changes needed, return empty updates
    if (updatedRoles.length === user.roles.length) {
        return {};
    }

    // Prepare updates for user document
    const updates: Partial<Doc<"users">> = {
        roles: updatedRoles
    };

    // Handle role-specific cleanup
    for (const roleToRemove of rolesToRemove) {
        switch (roleToRemove) {
            case Roles.CUSTOMER:
                // For customers, we can't mark them as inactive since there's no isActive field
                // The role removal from the user is sufficient
                break;

            case Roles.COACH:
                // Mark coach as inactive
                if (user.coachId) {
                    await ctx.db.patch(user.coachId, { isActive: false });
                }
                break;

            case Roles.FACILITY_MANAGER:
                // For facility managers, we can't mark them as inactive since there's no isActive field
                // The role removal from the user is sufficient
                break;
        }
    }

    return updates;
}
