import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
    const baseUrl = 'https://curatd.com'
    const locales = ['en', 'fr', 'pt']

    const pages = [
        {
            url: '',
            priority: 1.0,
            changeFrequency: 'weekly' as const,
        },
        {
            url: '/coach',
            priority: 0.9,
            changeFrequency: 'weekly' as const,
        },
        {
            url: '/gym',
            priority: 0.9,
            changeFrequency: 'weekly' as const,
        },
        {
            url: '/contact',
            priority: 0.7,
            changeFrequency: 'monthly' as const,
        },
        {
            url: '/privacy',
            priority: 0.5,
            changeFrequency: 'monthly' as const,
        },
    ]

    const sitemap: MetadataRoute.Sitemap = []

    // Add pages for each locale
    locales.forEach(locale => {
        pages.forEach(page => {
            sitemap.push({
                url: `${baseUrl}/${locale}${page.url}`,
                lastModified: new Date(),
                changeFrequency: page.changeFrequency,
                priority: page.priority,
            })
        })
    })

    // Add default language pages (without locale prefix)
    pages.forEach(page => {
        sitemap.push({
            url: `${baseUrl}${page.url}`,
            lastModified: new Date(),
            changeFrequency: page.changeFrequency,
            priority: page.priority,
        })
    })

    return sitemap
} 