"use client";

import { useI18n } from "@curatd/shared/locales/client";
import { Badge } from "@curatd/ui/components/badge";
import { Title } from "./title";
import { VideoCarousel } from "./video-carousel";

export function PlaceSection() {
    const t = useI18n();

    const placesData = [
        {
            title: t("landing.places.items.atHome.title"),
            subtitle: t("landing.places.items.atHome.subtitle"),
            description: t("landing.places.items.atHome.description")
        },
        {
            title: t("landing.places.items.publicSpaces.title"),
            subtitle: t("landing.places.items.publicSpaces.subtitle"),
            description: t("landing.places.items.publicSpaces.description")
        },
        {
            title: t("landing.places.items.partnerFacilities.title"),
            subtitle: t("landing.places.items.partnerFacilities.subtitle"),
            description: t("landing.places.items.partnerFacilities.description")
        }
    ];
    const videoUrl = "https://videos.pexels.com/video-files/12188782/12188782-uhd_2560_1440_25fps.mp4"

    return (
        <section id="places" className="bg-background py-8 sm:py-12 md:py-16 lg:py-20">
            <div className="md:text-center">
                <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <Title
                        title={t("landing.places.title")}
                        titleHighlight={t("landing.places.titleHighlight")}
                        subtitle={t("landing.places.subtitle")}
                        showSeparator={true}
                        as="h1"
                    />
                </div>
            </div>
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <VideoCarousel
                    data={placesData}
                    videoUrl={videoUrl}
                    renderContent={(item) => (
                        <>
                            <Badge variant="secondary" className="mb-3">
                                {item.subtitle ?? ""}
                            </Badge>
                            <h2 className="text-2xl md:text-3xl font-bold mb-3">
                                {item.title ?? ""}
                            </h2>
                            <p className="text-base md:text-lg opacity-80">
                                {item.description ?? ""}
                            </p>
                        </>
                    )}
                />
            </div>
        </section>
    );
}
