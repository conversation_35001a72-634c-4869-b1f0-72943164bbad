"use client";

import { useState } from "react";
import { Button } from "@curatd/ui/components/button";
import { Input } from "@curatd/ui/components/input";
import { Label } from "@curatd/ui/components/label";
import { Textarea } from "@curatd/ui/components/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@curatd/ui/components/card";
import { motion } from "motion/react";
import { useCreatePreRegistration, DuplicatePreRegistrationError } from "@curatd/shared/backend/use-cases/pre-registration/client";
import type { PreRegistrationType } from "@curatd/shared/backend/use-cases/pre-registration/core";
import { useI18n } from "@curatd/shared/locales/client";

interface PreRegistrationFormProps {
    type: PreRegistrationType;
    formTitle: string;
    formDescription?: string;
    className?: string;
    onSuccess?: () => void;
    onDuplicate?: () => void;
    onError?: (error: unknown) => void;
}

interface FormData {
    fullName: string;
    email: string;
    sportTypes: string;
    gymName: string;
    address: string;
    phone: string;
    message: string;
}

export function PreRegistrationForm({
    type,
    formTitle,
    formDescription,
    className = "",
    onSuccess,
    onDuplicate,
    onError,
}: PreRegistrationFormProps) {
    const t = useI18n();
    const [formData, setFormData] = useState<FormData>({
        fullName: "",
        email: "",
        sportTypes: "",
        gymName: "",
        address: "",
        phone: "",
        message: "",
    });
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [isAlreadyRegistered, setIsAlreadyRegistered] = useState(false);
    const createPreRegistration = useCreatePreRegistration();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validation based on type
        if (type === "coach" && (!formData.email || !formData.sportTypes)) {
            onError?.(new Error(t("landing.preRegistration.errors.fillRequired")));
            return;
        }
        if (type === "gym" && (!formData.gymName || !formData.email || !formData.address)) {
            onError?.(new Error(t("landing.preRegistration.errors.fillRequired")));
            return;
        }
        if (type === "user" && !formData.email) {
            onError?.(new Error(t("landing.preRegistration.errors.emailRequired")));
            return;
        }

        try {
            // Prepare data based on type
            const submitData = {
                type,
                email: formData.email,
                ...(type === "coach" && {
                    fullName: formData.fullName,
                    sportTypes: formData.sportTypes,
                }),
                ...(type === "gym" && {
                    gymName: formData.gymName,
                    address: formData.address,
                    phone: formData.phone,
                    message: formData.message,
                }),
                ...(type === "user" && {
                    fullName: formData.fullName || formData.email,
                }),
            };

            await createPreRegistration.trigger(submitData);

            console.log("Pre-registration submitted:", submitData);
            setIsSubmitted(true);
            setFormData({
                fullName: "",
                email: "",
                sportTypes: "",
                gymName: "",
                address: "",
                phone: "",
                message: "",
            });

            onSuccess?.();

            // Reset success message after 3 seconds
            setTimeout(() => {
                setIsSubmitted(false);
            }, 3000);
        } catch (error) {
            if (error instanceof DuplicatePreRegistrationError) {
                console.log("User already pre-registered");
                setIsAlreadyRegistered(true);
                setFormData({
                    fullName: "",
                    email: "",
                    sportTypes: "",
                    gymName: "",
                    address: "",
                    phone: "",
                    message: "",
                });

                onDuplicate?.();

                // Reset already registered message after 3 seconds
                setTimeout(() => {
                    setIsAlreadyRegistered(false);
                }, 3000);
            } else {
                console.error("Pre-registration failed:", error);
                onError?.(error);
            }
        }
    };

    const handleInputChange = (field: keyof FormData, value: string) => {
        setFormData(prev => ({ ...prev, [field]: value }));
    };

    const renderFormFields = () => {
        switch (type) {
            case "coach":
                return (
                    <>
                        <div className="space-y-2">
                            <Label htmlFor="fullName" className="font-bold">{t("landing.preRegistration.coach.fullName.label")}</Label>
                            <Input
                                id="fullName"
                                type="text"
                                placeholder={t("landing.preRegistration.coach.fullName.placeholder")}
                                value={formData.fullName}
                                onChange={(e) => handleInputChange("fullName", e.target.value)}
                                className="h-12"
                                disabled={createPreRegistration.isMutating}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="email" className="font-bold">{t("landing.preRegistration.coach.email.label")} *</Label>
                            <Input
                                id="email"
                                type="email"
                                placeholder={t("landing.preRegistration.coach.email.placeholder")}
                                value={formData.email}
                                onChange={(e) => handleInputChange("email", e.target.value)}
                                required
                                className="h-12"
                                disabled={createPreRegistration.isMutating}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="sportTypes" className="font-bold">{t("landing.preRegistration.coach.sportTypes.label")} *</Label>
                            <Textarea
                                id="sportTypes"
                                placeholder={t("landing.preRegistration.coach.sportTypes.placeholder")}
                                value={formData.sportTypes}
                                onChange={(e) => handleInputChange("sportTypes", e.target.value)}
                                required
                                className="min-h-[100px] resize-none"
                                disabled={createPreRegistration.isMutating}
                            />
                        </div>
                    </>
                );

            case "gym":
                return (
                    <>
                        <div className="space-y-2">
                            <Label htmlFor="gymName" className="font-bold">{t("landing.preRegistration.gym.gymName.label")} *</Label>
                            <Input
                                id="gymName"
                                type="text"
                                placeholder={t("landing.preRegistration.gym.gymName.placeholder")}
                                value={formData.gymName}
                                onChange={(e) => handleInputChange("gymName", e.target.value)}
                                required
                                className="h-12"
                                disabled={createPreRegistration.isMutating}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="email" className="font-bold">{t("landing.preRegistration.gym.email.label")} *</Label>
                            <Input
                                id="email"
                                type="email"
                                placeholder={t("landing.preRegistration.gym.email.placeholder")}
                                value={formData.email}
                                onChange={(e) => handleInputChange("email", e.target.value)}
                                required
                                className="h-12"
                                disabled={createPreRegistration.isMutating}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="address" className="font-bold">{t("landing.preRegistration.gym.address.label")} *</Label>
                            <Input
                                id="address"
                                type="text"
                                placeholder={t("landing.preRegistration.gym.address.placeholder")}
                                value={formData.address}
                                onChange={(e) => handleInputChange("address", e.target.value)}
                                required
                                className="h-12"
                                disabled={createPreRegistration.isMutating}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="phone" className="font-bold">{t("landing.preRegistration.gym.phone.label")}</Label>
                            <Input
                                id="phone"
                                type="tel"
                                placeholder={t("landing.preRegistration.gym.phone.placeholder")}
                                value={formData.phone}
                                onChange={(e) => handleInputChange("phone", e.target.value)}
                                className="h-12"
                                disabled={createPreRegistration.isMutating}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="message" className="font-bold" >{t("landing.preRegistration.gym.message.label")}</Label>
                            <Textarea
                                id="message"
                                placeholder={t("landing.preRegistration.gym.message.placeholder")}
                                value={formData.message}
                                onChange={(e) => handleInputChange("message", e.target.value)}
                                className="min-h-[120px] resize-none"
                                disabled={createPreRegistration.isMutating}
                            />
                        </div>
                    </>
                );

            case "user":
            default:
                return (
                    <div className="flex flex-col sm:flex-row gap-4">
                        <Input
                            type="email"
                            placeholder={t("landing.preRegistration.user.email.placeholder")}
                            value={formData.email}
                            onChange={(e) => handleInputChange("email", e.target.value)}
                            required
                            className="flex-1 h-12 text-base"
                            disabled={createPreRegistration.isMutating}
                        />
                        <Button
                            type="submit"
                            disabled={createPreRegistration.isMutating || !formData.email}
                            className="h-12 px-8 bg-foreground text-background hover:bg-foreground/90 text-base font-semibold"
                        >
                            {createPreRegistration.isMutating ? t("landing.preRegistration.user.submitting") : getSubmitButtonText()}
                        </Button>
                    </div>
                );
        }
    };

    const getSubmitButtonText = () => {
        switch (type) {
            case "coach":
                return t("landing.preRegistration.coach.cta");
            case "gym":
                return t("landing.preRegistration.gym.cta");
            case "user":
            default:
                return t("landing.preRegistration.user.cta");
        }
    };

    const getSuccessMessage = () => {
        switch (type) {
            case "coach":
                return {
                    title: t("landing.preRegistration.coach.successTitle"),
                    message: t("landing.preRegistration.coach.successMessage")
                };
            case "gym":
                return {
                    title: t("landing.preRegistration.gym.successTitle"),
                    message: t("landing.preRegistration.gym.successMessage")
                };
            case "user":
            default:
                return {
                    title: t("landing.preRegistration.user.successTitle"),
                    message: t("landing.preRegistration.user.successMessage")
                };
        }
    };

    const getAlreadyRegisteredMessage = () => {
        switch (type) {
            case "coach":
                return {
                    title: t("landing.preRegistration.coach.duplicateTitle"),
                    message: t("landing.preRegistration.coach.duplicateMessage")
                };
            case "gym":
                return {
                    title: t("landing.preRegistration.gym.duplicateTitle"),
                    message: t("landing.preRegistration.gym.duplicateMessage")
                };
            case "user":
            default:
                return {
                    title: t("landing.preRegistration.user.duplicateTitle"),
                    message: t("landing.preRegistration.user.duplicateMessage")
                };
        }
    };

    return (
        <div className={className}>
            <Card className="max-w-2xl mx-auto bg-card/80 backdrop-blur-sm border-border/50 shadow-lg pt-6">
                <CardHeader className="pb-6">
                    <CardTitle className="text-xl sm:text-2xl font-bold text-center">
                        {formTitle}
                    </CardTitle>
                    {formDescription && (
                        <CardDescription className="text-center">
                            {formDescription}
                        </CardDescription>
                    )}
                </CardHeader>
                <CardContent>
                    {!isSubmitted && !isAlreadyRegistered ? (
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {renderFormFields()}
                            {(type === "coach" || type === "gym") && (
                                <Button
                                    type="submit"
                                    disabled={createPreRegistration.isMutating}
                                    className="w-full h-12 text-lg font-semibold rounded-full"
                                    size="lg"
                                >
                                    {createPreRegistration.isMutating ? t("landing.preRegistration.submitting") : getSubmitButtonText()}
                                </Button>
                            )}
                            {type === "user" && (
                                <p className="text-sm text-muted-foreground text-center">
                                    {t("landing.preRegistration.user.privacyNote")}
                                </p>
                            )}
                        </form>
                    ) : isSubmitted ? (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="text-center py-8"
                        >
                            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold text-foreground mb-2">
                                {getSuccessMessage().title}
                            </h3>
                            <p className="text-muted-foreground">
                                {getSuccessMessage().message}
                            </p>
                        </motion.div>
                    ) : (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="text-center py-8"
                        >
                            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h3 className="text-xl font-semibold text-foreground mb-2">
                                {getAlreadyRegisteredMessage().title}
                            </h3>
                            <p className="text-muted-foreground">
                                {getAlreadyRegisteredMessage().message}
                            </p>
                        </motion.div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
} 