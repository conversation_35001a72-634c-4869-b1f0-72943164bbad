"use client";

import * as React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IconFilter } from "@tabler/icons-react";
import { useI18n } from "@curatd/shared/locales/client";
import { Button } from "@curatd/ui/components/button";
import { Badge } from "@curatd/ui/components/badge";
import { UserFilters } from "@/components/users/user-filters";
import { UserDataTable } from "@/components/users/user-data-table";
import { InviteUserDialog } from "@/components/invitations/invite-user-dialog";
import { Enums } from "@curatd/backend";

export default function UsersPage() {
  const t = useI18n();
  const [searchTerm, setSearchTerm] = React.useState("");
  const [roleFilter, setRoleFilter] = React.useState<Enums.Roles[]>([]);
  const [inviteDialogOpen, setInviteDialogOpen] = React.useState(false);

  // Debounce search term to avoid excessive API calls
  const [debouncedSearchTerm, setDebouncedSearchTerm] = React.useState("");

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Convert filter values for the API
  const hasActiveFilters = roleFilter.length > 0 || searchTerm.trim() !== "";
  const activeFiltersCount = [
    roleFilter.length > 0,
    searchTerm.trim() !== "",
  ].filter(Boolean).length;

  return (
    <div className="flex flex-col h-full">
      <div className="border-b p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h1 className="text-3xl font-bold">
              {t("admin.userManagement.title")}
            </h1>
            {hasActiveFilters && (
              <Badge variant="secondary" className="text-xs font-medium">
                <IconFilter className="mr-1 h-3 w-3" />
                {activeFiltersCount}
              </Badge>
            )}
          </div>
          <Button onClick={() => setInviteDialogOpen(true)}>
            <IconUserPlus className="mr-2 h-4 w-4" />
            {t("admin.userManagement.inviteUser")}
          </Button>
        </div>
        <p className="text-muted-foreground mt-2">
          {t("admin.userManagement.description")}
        </p>
      </div>

      <UserFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        roleFilter={roleFilter}
        onRoleFilterChange={setRoleFilter}
      />

      <div className="flex-1 p-6">
        <UserDataTable
          searchTerm={debouncedSearchTerm}
          roleFilter={roleFilter}
        />
      </div>

      <InviteUserDialog
        open={inviteDialogOpen}
        onOpenChange={setInviteDialogOpen}
      />
    </div>
  );
}
