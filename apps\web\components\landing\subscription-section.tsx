"use client";

import { But<PERSON> } from "@curatd/ui/components/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@curatd/ui/components/card";
import { useI18n } from "@curatd/shared/locales/client";
import { Check } from "lucide-react";
import { Title } from "./title";

export function SubscriptionSection() {
    const t = useI18n();

    return (
        <section id="subscription" className="py-8 sm:py-12 md:py-16 lg:py-20 bg-background">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                <div className="max-w-7xl mx-auto">
                    {/* Header */}
                    <div className="text-center mb-8 sm:mb-12 md:mb-16">
                        <Title
                            title={t("landing.subscription.title")}
                            subtitle={t("landing.subscription.subtitle")}
                            showSeparator={true}
                            animated={true}
                        />
                    </div>

                    {/* Pricing Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                        {/* No Subscription (Pay Per Class) */}
                        <Card className="relative h-full text-foreground border-foreground/10 shadow-lg flex flex-col justify-between">
                            <CardHeader className="text-center pb-6 sm:pb-8 pt-6 sm:pt-8 px-4 sm:px-6">
                                <CardTitle className="text-xl sm:text-2xl font-bold mb-2">
                                    {t("landing.subscription.payPerClass.title")}
                                </CardTitle>
                                <div className="mb-3 sm:mb-4">
                                    <div className="flex items-center justify-center space-x-2 mb-2">
                                        <div className="text-2xl sm:text-3xl md:text-4xl font-bold text-foreground">
                                            {t("landing.subscription.payPerClass.price")}
                                        </div>
                                    </div>
                                    <div className="text-sm sm:text-base text-foreground/70">
                                        {t("landing.subscription.payPerClass.priceUnit")}
                                    </div>
                                </div>
                                <p className="text-sm sm:text-base text-foreground/60">
                                    {t("landing.subscription.payPerClass.description")}
                                </p>
                            </CardHeader>
                            <CardContent className="space-y-4 px-4 sm:px-6">
                                <div className="space-y-3">
                                    <div className="flex items-center space-x-3">
                                        <Check className="h-4 w-4 sm:h-5 sm:w-5 text-foreground flex-shrink-0" />
                                        <span className="text-xs sm:text-sm font-medium">
                                            {t("landing.subscription.payPerClass.features.access")}
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-3">
                                        <Check className="h-4 w-4 sm:h-5 sm:w-5 text-foreground flex-shrink-0" />
                                        <span className="text-xs sm:text-sm font-medium">
                                            {t("landing.subscription.payPerClass.features.booking")}
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-3">
                                        <Check className="h-4 w-4 sm:h-5 sm:w-5 text-foreground flex-shrink-0" />
                                        <span className="text-xs sm:text-sm font-medium">
                                            {t("landing.subscription.payPerClass.features.flexibility")}
                                        </span>
                                    </div>

                                </div>
                            </CardContent>
                            <CardFooter
                                className="flex justify-end"
                            >
                                <Button className="w-full text-base sm:text-lg py-4 sm:py-6 bg-foreground text-background touch-manipulation hover:bg-foreground/80">
                                    {t("landing.subscription.payPerClass.cta")}
                                </Button>
                            </CardFooter>
                        </Card>
                        {/* Subscription */}
                        <Card className="relative h-full border-primary shadow-lg justify-between">
                            <CardHeader className="text-center pb-6 sm:pb-8 pt-6 sm:pt-8 px-4 sm:px-6">
                                <CardTitle className="text-xl sm:text-2xl font-bold mb-2">
                                    {t("landing.subscription.subscription.title")}
                                </CardTitle>
                                <div className="mb-3 sm:mb-4">
                                    <div className="flex items-center justify-center space-x-2 mb-2">
                                        <div className="text-2xl sm:text-3xl md:text-4xl font-bold text-primary">
                                            {t("landing.subscription.subscription.price")}
                                        </div>
                                    </div>
                                    <div className="text-sm sm:text-base text-muted-foreground">
                                        {t("landing.subscription.subscription.priceUnit")}
                                    </div>
                                </div>
                                <p className="text-sm sm:text-base text-muted-foreground">
                                    {t("landing.subscription.subscription.description")}
                                </p>
                            </CardHeader>
                            <CardContent className="space-y-4 px-4 sm:px-6 justify-end">
                                <div className="space-y-3">
                                    <div className="flex items-center space-x-3">
                                        <Check className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 flex-shrink-0" />
                                        <span className="text-xs sm:text-sm font-medium">
                                            {t("landing.subscription.subscription.features.discount")}
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-3">
                                        <Check className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 flex-shrink-0" />
                                        <span className="text-xs sm:text-sm font-medium">
                                            {t("landing.subscription.subscription.features.priority")}
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-3">
                                        <Check className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 flex-shrink-0" />
                                        <span className="text-xs sm:text-sm font-medium">
                                            {t("landing.subscription.subscription.features.exclusive")}
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-3">
                                        <Check className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 flex-shrink-0" />
                                        <span className="text-xs sm:text-sm font-medium">
                                            {t("landing.subscription.subscription.features.invites")}
                                        </span> 
                                    </div>
                                </div>
                            </CardContent>
                            <CardFooter
                                className="flex justify-end"
                            >
                                <Button className="w-full text-base sm:text-lg py-4 sm:py-6 bg-foreground text-background touch-manipulation hover:bg-foreground/80">
                                    {t("landing.subscription.subscription.cta")}
                                </Button>
                            </CardFooter>
                        </Card>
                    </div>
                </div>
            </div>
        </section>
    );
} 