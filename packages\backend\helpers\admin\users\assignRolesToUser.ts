import { MutationCtx } from "../../../convex/_generated/server";
import { Doc, Id } from "../../../convex/_generated/dataModel";
import { Roles, StripeConnectStatus } from "../../../types/enums";

/**
 * Helper function to assign roles to a user and create associated entities
 * Following Convex guidelines for helper functions
 */
export async function assignRolesToUser(
    ctx: MutationCtx,
    userId: Id<"users">,
    rolesToAdd: Roles[]
): Promise<Partial<Doc<"users">>> {
    const user = await ctx.db.get<"users">(userId);
    if (!user) {
        throw new Error("User not found");
    }

    // Filter out roles that are already assigned
    const newRoles = rolesToAdd.filter(role => !user.roles.includes(role));

    // If no new roles to add, return empty updates
    if (newRoles.length === 0) {
        return {};
    }

    // Prepare updates for user document
    const updates: Partial<Doc<"users">> = {
        roles: [...user.roles, ...newRoles]
    };

    // Create associated entities for new roles
    for (const role of newRoles) {
        switch (role) {
            case Roles.CUSTOMER:
                // Create customer entity
                const customerId = await ctx.db.insert("customers", {
                    userId: userId,
                    profile: {
                        firstName: user.firstName || "",
                        lastName: user.lastName || ""
                    },
                    onboarding: {
                        preferredSports: [],
                        preferredTimes: [],
                        preferredLocations: []
                    },
                    calendarId: await ctx.db.insert("calendars", {
                        ownerType: "customer",
                        ownerId: userId,
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                    }),
                    createdAt: new Date().toISOString()
                });
                updates.customerId = customerId;
                break;

            case Roles.COACH:
                // Create coach entity
                const coachId = await ctx.db.insert("coaches", {
                    userId: userId,
                    legal: {
                        companyName: "",
                        vat: "",
                        address: ""
                    },
                    stripeConnectStatus: StripeConnectStatus.NOT_CONNECTED,
                    profile: {
                        bio: "",
                        sports: []
                    },
                    vacationMode: false,
                    isActive: true,
                    calendarId: await ctx.db.insert("calendars", {
                        ownerType: "coach",
                        ownerId: userId,
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                    }),
                    createdAt: new Date().toISOString()
                });
                updates.coachId = coachId;
                break;

            case Roles.FACILITY_MANAGER:
                // Create facility manager entity
                const facilityManagerId = await ctx.db.insert("facilityManagers", {
                    userId: userId,
                    profile: {
                        contactName: `${user.firstName || ""} ${user.lastName || ""}`.trim()
                    },
                    calendarId: await ctx.db.insert("calendars", {
                        ownerType: "facilityManager",
                        ownerId: userId,
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                    }),
                    createdAt: new Date().toISOString()
                });
                updates.facilityManagerId = facilityManagerId;
                break;
        }
    }

    return updates;
}
