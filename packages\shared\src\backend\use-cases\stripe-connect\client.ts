"use client";

import { useState } from "react";
import {
    type CreateAccountLinkInput,
    CreateAccountLinkSchema,
} from "./core";

/**
 * Hook to create a Stripe account link for onboarding.
 * This hook is intended to be used from a server action or an API route handler
 * in the specific application (e.g., `apps/web` or `apps/admin`).
 *
 * @returns A function to trigger the account link creation.
 *
 * @example
 * ```typescript
 * // In a React component
 * const { createLink, isConnecting } = useCreateStripeAccountLink();
 *
 * const handleOnboard = async () => {
 *   const result = await createLink({
 *     userId: "...",
 *     profileType: "coach",
 *     returnUrl: "https://example.com/return",
 *     refreshUrl: "https://example.com/refresh",
 *   });
 *
 *   if (result.success) {
 *     window.location.href = result.data.url;
 *   } else {
 *     // Handle error
 *     console.error(result.error);
 *   }
 * };
 * ```
 */
export function useCreateStripeAccountLink() {
    const [isConnecting, setIsConnecting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const createLink = async (input: Omit<CreateAccountLinkInput, 'userId' | 'returnUrl' | 'refreshUrl'>) => {
        // Frontend validation
        const validation = CreateAccountLinkSchema.omit({
            userId: true,
            returnUrl: true,
            refreshUrl: true
        }).safeParse(input);

        if (!validation.success) {
            const errorMessage = "Invalid input for Stripe account link.";
            setError(errorMessage);
            return { success: false, error: errorMessage };
        }

        setIsConnecting(true);
        setError(null);

        try {
            // Call the API route to create the account link
            const response = await fetch("/api/stripe/account-link", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(input),
            });

            if (!response.ok) {
                const { error: apiError } = await response.json();
                throw new Error(apiError || "Failed to create Stripe account link.");
            }

            const data = await response.json();
            setIsConnecting(false);
            return { success: true, data };
        } catch (err: any) {
            const errorMessage = err.message || "An unexpected error occurred.";
            setError(errorMessage);
            setIsConnecting(false);
            return { success: false, error: errorMessage };
        }
    };

    return { createLink, isConnecting, error };
} 