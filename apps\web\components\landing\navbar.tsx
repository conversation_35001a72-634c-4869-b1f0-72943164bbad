"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { useI18n } from "@curatd/shared/locales/client";
import { Button } from "@curatd/ui/components/button";
import { cn } from "@curatd/ui/lib/utils";
import { Menu, X } from "lucide-react";
import CuratdLogo from "@curatd/ui/components/curatd-logo";
import { ThemeSwitcher } from "@curatd/ui/components/theme-switcher";
import LanguageSwitcher from "@curatd/ui/components/language-switcher";
import Link from "next/link";

interface NavbarProps {
  className?: string;
  isHome?: boolean;
}

export function Navbar({ className, isHome = false }: NavbarProps) {
  const [isOpen, setIsOpen] = useState(false);
  const t = useI18n();


  const navigationItems = [
    { label: t("nav.athlete") || "For Athletes", href: "/", key: "athlete" },
    { label: t("nav.coach"), href: "/coach", key: "coach" },
    { label: t("nav.gym"), href: "/gym", key: "gym" },
    { label: t("nav.contact"), href: "/contact", key: "contact" },
  ];

  const handleNavClick = (href: string) => {
    setIsOpen(false);
    if (href.startsWith("#")) {
      // Handle anchor links for sections on the same page
      const element = document.querySelector(href);
      if (element) {
        const offsetTop = (element as HTMLElement).offsetTop - 80; // Account for fixed navbar height
        window.scrollTo({
          top: offsetTop,
          behavior: "smooth",
        });
      }
    } else {
      // Handle page navigation
      window.location.href = href;
    }
  };

  return (
    <div className="max-w-8xl mx-auto px-8 fixed bottom-4 md:top-4 left-0 right-0 z-50 h-20">
      <motion.nav
        className={cn(
          "relative transition-all duration-75 rounded-full bg-card/80 backdrop-blur-md shadow-sm",
          className
        )}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="px-4 sm:px-6 md:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <CuratdLogo onClick={() => handleNavClick(isHome ? "#hero" : "/")} size="large" />

            {/* Desktop Navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-center space-x-4">
                {navigationItems.map((item, index) => (
                  <motion.button
                    key={item.key}
                    onClick={() => handleNavClick(item.href)}
                    className="text-foreground hover:text-foreground/60 px-3 py-2 rounded-md text-base font-bold transition-colors cursor-pointer"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    {item.label}
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Theme Switcher, Language Switcher & Mobile Menu Button */}
            <div className="flex items-center gap-2">
              <div className="hidden md:inline-flex">
                <ThemeSwitcher variant="compact" size="lg" />
              </div>
              <div className="hidden md:inline-flex">
                <LanguageSwitcher size="lg" />
              </div>
              {/* Join now button (desktop & mobile) */}

              <Button
                asChild
                className="rounded-full"
                variant="default"
                size="lg"
              >
                <Link href="#join-now" className="text-primary-foreground">{t('web.acceptInvitation.acceptButton')}</Link>
              </Button>
              {/* Mobile menu button */}
              <div className="md:hidden">
                <Button
                  variant="ghost"
                  size="lg"
                  onClick={() => setIsOpen(!isOpen)}
                  className="inline-flex items-center justify-center p-2"
                >
                  <span className="sr-only">
                    {isOpen ? t("nav.closeMenu") : t("nav.openMenu")}
                  </span>
                  {isOpen ? (
                    <X className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Menu className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Navigation Dropdown */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              className="absolute bottom-full left-0 right-0 mb-2 z-50 md:hidden"
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{
                type: "spring",
                damping: 20,
                stiffness: 300,
                duration: 0.25,
              }}
            >
              {/* Mobile Menu Content */}
              <div className="bg-background rounded-3xl shadow-2xl overflow-hidden">
                {/* Navigation Items */}
                <div className="p-4 space-y-1">
                  {navigationItems.map((item, index) => (
                    <motion.button
                      key={item.key}
                      onClick={() => handleNavClick(item.href)}
                      className="flex cursor-pointer items-center w-full text-left px-5 py-3.5 text-base font-medium text-foreground hover:text-accent-foreground hover:bg-accent rounded-2xl transition-all duration-75 group"
                      initial={{ opacity: 0, x: -15 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{
                        delay: index * 0.06 + 0.1,
                        duration: 0.2,
                        ease: "easeOut",
                      }}
                      whileHover={{ x: 2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="group-hover:translate-x-1 transition-transform duration-200 ease-out">
                        {item.label}
                      </span>
                    </motion.button>
                  ))}

                  {/* Theme and Language Switchers (mobile) */}
                  <div className="pt-2 mt-2 border-t border-border">
                    <div className="flex items-center justify-between px-5 py-3.5">
                      <span className="text-sm font-medium text-muted-foreground">Theme</span>
                      <ThemeSwitcher variant="compact" size="lg" />
                    </div>
                    <div className="flex items-center justify-between px-5 py-3.5">
                      <span className="text-sm font-medium text-muted-foreground">Language</span>
                      <LanguageSwitcher size="lg" />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>
    </div>
  );
}
