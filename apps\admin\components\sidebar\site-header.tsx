"use client";

import { useCurrentLocale } from "@curatd/shared/locales/client";
import { useAdminNavigation } from "@curatd/ui/hooks/use-admin-navigation";
import { SiteHeaderBase } from "@curatd/ui/components/site-header-base";

export function SiteHeader() {
  const locale = useCurrentLocale();
  const { navItems, isNavItemActive } = useAdminNavigation();

  return (
    <SiteHeaderBase
      navItems={navItems}
      isNavItemActive={isNavItemActive}
      rootBreadcrumb={{
        title: "Admin",
        url: `/${locale}/`,
      }}
    />
  );
}
