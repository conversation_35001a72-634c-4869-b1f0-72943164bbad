"use client";

import { motion } from "motion/react";
import { CheckCircle, Sparkles } from "lucide-react";
import { useI18n } from "@curatd/shared/locales/client";
import { Button } from "@curatd/ui/components/button";
import { Card, CardContent } from "@curatd/ui/components/card";

interface OnboardingCompletionProps {
  onComplete: () => void;
}

export function OnboardingCompletion({
  onComplete,
}: OnboardingCompletionProps) {
  const t = useI18n();

  return (
    <div className="w-full max-w-2xl mx-auto">
      <Card className="text-center border-0 shadow-2xl bg-gradient-to-br from-primary/5 to-primary/10">
        <CardContent className="p-12 space-y-8">
          {/* Success Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{
              type: "spring",
              damping: 15,
              stiffness: 300,
              delay: 0.2,
            }}
            className="relative mx-auto w-24 h-24"
          >
            <div className="absolute inset-0 bg-primary/20 rounded-full animate-ping" />
            <div className="relative bg-primary text-primary-foreground rounded-full p-6 shadow-lg">
              <CheckCircle className="h-12 w-12" />
            </div>

            {/* Sparkles animation */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="absolute -top-2 -right-2"
            >
              <Sparkles className="h-6 w-6 text-yellow-500" />
            </motion.div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7 }}
              className="absolute -bottom-2 -left-2"
            >
              <Sparkles className="h-4 w-4 text-yellow-400" />
            </motion.div>
          </motion.div>

          {/* Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-foreground">
              {t("onboarding.roleSelection.completion.title")}
            </h2>
            <p className="text-xl text-primary font-semibold">
              {t("onboarding.roleSelection.completion.subtitle")}
            </p>
            <p className="text-muted-foreground max-w-md mx-auto">
              {t("onboarding.roleSelection.completion.description")}
            </p>
          </motion.div>

          {/* CTA Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <Button
              size="lg"
              onClick={onComplete}
              className="px-12 py-4 text-lg rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary"
            >
              <Sparkles className="h-5 w-5 mr-2" />
              {t("onboarding.roleSelection.completion.cta")}
            </Button>
          </motion.div>
        </CardContent>
      </Card>
    </div>
  );
}
